"""
backend/app/seeds/users.py
用户数据填充
"""
from app.models.user import User
from app.models.association import User_Role
from app.core.database import transactional_session
from app.core.security import get_password_hash

def seed_users():
   with transactional_session() as db:
     existing = db.query(User).count()
     if existing==0:
            
          # 创建用户
            admin = User(
                username="admin",
                password=get_password_hash("123456"),
                is_superuser=True,
                realname="管理员",
                email="<EMAIL>",
                department_id=1,
                phone="13900001111"
            )

            test_user = User(
                username="test",
                password=get_password_hash("123456"),
                realname="测试用户",
                email="<EMAIL>",
                department_id=2,
                phone="13801018888"
            )

            db.add_all([admin, test_user])
            db.flush()  # 获取 uid

          # 创建关联
            db.add_all([
               User_Role(user_id=admin.uid, role_id=1),
               User_Role(user_id=test_user.uid, role_id=2)
            ])

