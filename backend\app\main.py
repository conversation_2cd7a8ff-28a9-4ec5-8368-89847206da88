"""
backend/app/main.py
FastAPI应用的主入口
"""
from fastapi import FastAPI # FastAPI框架
from fastapi.staticfiles import StaticFiles # 静态文件
from fastapi.middleware.cors import CORSMiddleware # 跨域资源共享
import shutil

from fastapi.exceptions import RequestValidationError # 请求验证错误
from starlette.exceptions import HTTPException as StarletteHTTPException # HTTP错误
from app.core.response.exception_handlers import (http_exception_handler,validation_exception_handler,generic_exception_handler )# 异常处理程序

from fastapi.responses import FileResponse # 文件响应
from pathlib import Path

from app.core.config import settings # 自定义配置
from app.api import api_router

from app.models import *
from app.core.database import create_tables,delete_database
from app.seeds.seed_all import seed_all

import logging
logging.basicConfig(level=logging.DEBUG)

BAST_DIR=Path(__file__).parent
STATIC_DIR = BAST_DIR / "static"

async def lifespan(_: FastAPI):
    """
    应用程序生命周期管理
    """
    # 启动时执行
    print("🚀 应用程序启动中...")
    if settings.DEBUG and settings.INIT_DB:
        delete_database() # 开发模式，避免迁移
        create_tables()  # 创建数据库表
        print("✅ 数据库表创建完成")
        seed_all()
    yield
    # 关闭时执行
    clear_python_cache()
    print("🛑 应用程序关闭中...")

app=FastAPI(
    title=settings.PROJECT_NAME, # 项目名称
    version=settings.PROJECT_VERSION, # 项目版本
    description= settings.PROJECT_DESCRIPTION, # 项目描述
    debug=settings.DEBUG, # 调试模式
    lifespan=lifespan #生命周期管理
    )

# 异常处理程序
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)

# 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS, # 允许的源
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS, # 是否允许携带cookie
    allow_methods=settings.CORS_ALLOW_METHODS, # 允许的方法
    allow_headers=settings.CORS_ALLOW_HEADERS, # 允许的头
)


app.include_router(api_router, prefix=settings.API_PREFIX + settings.API_V1_STR)

app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static") # 挂载静态文件
@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    return FileResponse(STATIC_DIR /"favicon.ico")



def clear_python_cache():
    print("🧹 正在清理 Python 项目缓存文件...")
    try:
        for p in BAST_DIR.glob("**/__pycache__"):
            shutil.rmtree(p)  # ✅ 删除整个非空目录
    except Exception as e:
        print(f"❌ 清理 Python 项目缓存文件失败: {e}")
    else:
        print("✅ 清理 Python 项目缓存文件完成")
        

@app.get("/")
async def root():
    return {
        "message": "Hello World",
        "version": settings.PROJECT_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
        }