/**
 * frontend/src/types/nemu.ts
 * 路由类型定义
 */

//菜单类型
export const MenuType = {
  menu: 'menu',
  button: 'button'
} as const

export type MenuType = typeof MenuType[keyof typeof MenuType]

//菜单元数据
export interface MenuMeta {
  affix?: boolean
  cache?: boolean
  new_window?: boolean
  hidden?: boolean
  icon?: string
  title?: string
}

//后端返回的菜单数据
export interface LoginResponseMenu {
  uid: number
  name: string
  path: string
  sort?: number
  component?: string
  redirect?: string
  meta?: MenuMeta
  menu_type: MenuType
  operation?: string
  requires_auth: boolean
  children?: LoginResponseMenu[]
}

export interface ButtonPermission {
  uid: number
  name: string
  operation: string
}

