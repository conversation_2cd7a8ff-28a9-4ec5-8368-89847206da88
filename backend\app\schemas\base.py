"""
backend/app/api/schemas/base.py
API 基础请求/响应模型

Pydantic 模式
通过 Python 类来定义数据结构和其类型约束
创建一个蓝图，用来描述你的数据应该长什么样子，以及它应该包含哪些字段，每个字段的数据类型是什么。

主要特点：
1. 自动数据验证
2. 数据类型转换
3. 自动完成数据模型
4. 支持嵌套数据模型
5. 支持数据模型继承
6. 支持数据模型的序列化和反序列化

"""

from pydantic import BaseModel,Field
from datetime import datetime,timezone
from typing import Optional

class BaseReadSchema(BaseModel):
    #所有用于从ORM读取的模式的基类
    class Config:
        from_attributes = True #允许从ORM对象直接加载属性，这对于从数据库模型转换为Pydantic模式非常有用

class BaseIDSchema(BaseModel):
    #所有具有ID的模式的基类
    uid: int = Field(..., description="主键")

class AuditSchema(BaseModel):
    #包含审计信息的模式基类
    created_by: int = Field(..., description="创建人")
    updated_by: int = Field(..., description="更新人")

class TimeStampedSchema(BaseModel):
    #包含时间戳的模式基类
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(default=datetime.now(tz=timezone.utc), description="更新时间")

class BaseSchema(BaseIDSchema,TimeStampedSchema):
    #模式的通用基类，包含 id 和时间戳
    pass