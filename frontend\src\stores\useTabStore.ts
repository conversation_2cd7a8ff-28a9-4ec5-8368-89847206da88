// frontend/src/stores/useTabStore.ts

import { defineStore } from 'pinia'
import type { TabItem } from '@/components/tab/TabTpye';

export const useTabStore = defineStore('tab', {
  state: () => ({
    tabList: [] as { title: string; path: string; affix?: boolean }[],
    activeTab: ''
  }),
  actions: {
    addTab(tab: TabItem) {
        const exists=this.tabList.some(t=>t.path===tab.path)
        if (exists) return
        this.tabList.push(tab)
    },
    removeTab(path:string) {
      this.tabList = this.tabList.filter(t => t.path !== path)
    },
    setActiveTab(path:string) {
      this.activeTab = path
    },
    closeLeft(currentPath: string) {
    const index = this.tabList.findIndex(tab => tab.path === currentPath)
    this.tabList = this.tabList.filter((tab, i) => i >= index || tab.affix)
    },
    closeRight(currentPath: string) {
    const index = this.tabList.findIndex(tab => tab.path === currentPath)
    this.tabList = this.tabList.filter((tab, i) => i <= index || tab.affix)
  },
  closeOthers(currentPath: string) {
    this.tabList = this.tabList.filter(tab => tab.path === currentPath || tab.affix)
  },
  closeAll() {
    this.tabList = this.tabList.filter(tab => tab.affix)
    if (this.tabList.length > 0) {
      this.activeTab = this.tabList[0].path
    }
  },
  resetTabs() {
    this.tabList = []
    this.activeTab = ''
  }
  },
  persist: true // ✅ 开启持久化
})

