"""
backend/app/seeds/departments.py
部门数据填充
"""

from app.models.department import Department
from app.core.database import transactional_session #自动提交

def seed_departments():
    with transactional_session() as db:
        existing = db.query(Department).count()
        if existing==0:
            admin = Department(name="管理员", sort=1)
            it = Department(name="IT部门", sort=2)
            db.add_all([admin, it])
            db.flush()
            frontend = Department(name="前端部门", parent_id=it.uid, sort=1)
            backend = Department(name="后端部门", parent_id=it.uid, sort=2)
            db.add_all([frontend, backend])
