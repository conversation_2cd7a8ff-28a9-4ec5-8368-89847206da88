/**
 * frontend/src/utils/error.ts
 * 错误处理工具
 */


/**
 * 后端错误结构定义（可根据你的 API 响应结构扩展）
 */
interface ErrorWithCode {
  code: string
  message?: string
  status?: number
}

/**
 * 类型守卫：判断是否为带 code 的业务错误
 */
function isErrorWithCode(err: unknown): err is ErrorWithCode {
  return (
    typeof err === 'object' &&
    err !== null &&
    'code' in err &&
    typeof (err as Record<string, unknown>).code === 'string'
  )
}

/**
 * 提取错误信息：支持 Error、字符串、后端错误对象
 */
export function extractErrorMessage(err: unknown): string {
  if (err instanceof Error) return err.message
  if (typeof err === 'string') return err

  if (isErrorWithCode(err)) {
    return err.message ?? mapErrorCodeToMessage(err.code)
  }

  return '未知错误'
}

/**
 * 错误码映射为用户友好提示（可接入 i18n）
 */
function mapErrorCodeToMessage(code: string): string {
  const map: Record<string, string> = {
    USER_NOT_FOUND: '用户不存在',
    INVALID_PASSWORD: '密码错误',
    TOKEN_EXPIRED: '登录已过期，请重新登录',
    PERMISSION_DENIED: '权限不足',
    NETWORK_ERROR: '网络异常，请稍后重试',
    // 可继续扩展
  }

  return map[code] || `错误码：${code}`
}

/**
 * 判断是否为业务错误（可用于 catch 分支判断）
 */
export function isBusinessError(err: unknown): boolean {
  return isErrorWithCode(err)
}
