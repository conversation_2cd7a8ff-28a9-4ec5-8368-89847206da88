"""
backend/app/models/permissions.py
数据权限，控制用户业务数据可见范围
"""
import enum
from sqlalchemy import String,Integer,Enum,JSON
from sqlalchemy.orm import Mapped,mapped_column
from sqlalchemy.schema import UniqueConstraint

from app.core.database import Base
from app.models.base import BaseIDMixin

class TargetType(str,enum.Enum):
    # 目标类型
    user="user" # 用户级
    role="role" # 角色级
    department="department" # 部门级
    department_and_children="department_and_children" # 部门及子部门级
    all="all" # 全部

class PermissionSubject(str,enum.Enum):
    # 权限主体
    user="user" # 用户
    role="role" # 角色
    department="department" # 部门

class PermissionMode(str, enum.Enum):
    rule = "rule" # 规则权限
    resource = "resource" # 指定资源权限

class OperationType(str,enum.Enum):
    create = "c" # 创建
    read="r" # 读取
    write="w" # 写入
    delete="d" # 删除

class Data_Permission(Base,BaseIDMixin):
    # 数据权限表
    #   控制用户查询数据范围， 分为两种方式
    #   1.规则权限查询
    #   2.指定资源对象查询
    __tablename__="data_permissions"
    __table_args__ = (
        UniqueConstraint("subject_type", "subject_id", "resource_table","operation", name="uq_subject_resource"),
    )
    operation:Mapped[OperationType] = mapped_column(Enum(OperationType,native_enum=False), comment="操作类型")

    subject_type: Mapped[PermissionSubject] = mapped_column(Enum(PermissionSubject,native_enum=False), comment="权限主体")
    subject_id:Mapped[int] = mapped_column(Integer, nullable=False, comment="权限主体ID")

    mode:Mapped[PermissionMode] = mapped_column(Enum(PermissionMode,native_enum=False), comment="是否规则权限")
    rule_scope: Mapped[TargetType] = mapped_column(Enum(TargetType,native_enum=False), comment="规则类型")    

    resource_table:Mapped[str] = mapped_column(String(50), nullable=False, comment="资源表") #资源模型（后续业务表）
    resource_id:Mapped[JSON] = mapped_column(JSON, nullable=True, comment="资源ID")  #资源ID  当规则权限时，则无具体对象，而是表述资源类按照规则权限范围
