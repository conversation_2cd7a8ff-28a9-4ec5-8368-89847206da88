<!-- frontend/src/layouts/components/SidebarItem.vue -->
 <!-- 侧边栏菜单项 -->
<template>
    <n-space vertical>
    <n-layout has-sider>
      <n-layout-sider
        bordered
        collapse-mode="width"
        :collapsed-width="64"
        :width="240"
        :collapsed="collapsed"
        show-trigger
        @collapse="collapsed = true"
        @expand="collapsed = false"
        style="height: 100%;"
      >
        <n-menu
          v-model:value="activeKey"
          :collapsed="collapsed"
          :collapsed-width="64"
          :collapsed-icon-size="22"
          :options="menuOptions"
          @update:value="handleMenuSelect"
          style="height: 100%;"
        />
      </n-layout-sider>
    </n-layout>
  </n-space>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter,useRoute} from 'vue-router'
import { useUserStore } from '@/stores/user'


const userStore = useUserStore()
const menuOptions = userStore.menuOptions
const activeKey=ref<string | null>(null)
const collapsed = ref(false)

const router=useRouter() //路由
const route = useRoute() //路由器

onMounted(()=>{
  activeKey.value=route.path
})

function handleMenuSelect(key: string) {
 if (key) router.push(key)
}

</script>

<style>

</style>
