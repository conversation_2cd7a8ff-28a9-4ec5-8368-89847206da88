<template>
 <SplitViewLayout :left-width="280">
    <!-- 左侧：组织架构树 -->
    <template #left>
      <OrgTree @select="handleOrgSelect" />
    </template>

    <!-- 右侧：用户列表 -->
    <template #right>
      <UserTable :org-id="selectedOrgId" />
    </template>
  </SplitViewLayout>
</template>

<script lang="ts" setup>
import SplitViewLayout from '@/layouts/common/SplitViewLayout.vue'
import { ref } from 'vue'

const selectedOrgId = ref<string | null>(null)
function handleOrgSelect(id: string) {
  selectedOrgId.value = id
}
</script>

<style>

</style>
