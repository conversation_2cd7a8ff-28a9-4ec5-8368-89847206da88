  <!-- frontend/src/layouts/IndexPage.vue -->
 <!-- 主布局 -->
  <template>
    <n-flex style="height: 100vh; display: flex; flex-direction: column; overflow: hidden;">
      <HeaderItem/>
      <n-layout style="flex: 1;display: flex;">
        <n-flex>
        <SidebarItem/>
        <ContentItem/>
        </n-flex>
      </n-layout>
      <FooterItem />
    </n-flex>
  </template>
  
  <script lang="ts" setup>
  import SidebarItem from './components/SidebarItem.vue'
  import HeaderItem from './components/HeaderItem.vue'
  import FooterItem from './components/FooterItem.vue'
  import ContentItem from './components/ContentItem.vue'
  
  </script>
  
  <style>
  
  </style>