"""
backend/run.py
启动脚本，用于全局启动 FastAPI服务
"""

import uvicorn # uvicorn 服务
import argparse # 命令行参数解析
import os


parser = argparse.ArgumentParser()
parser.add_argument("--init-db", action="store_true", help="初始化数据库")
args = parser.parse_args()

if args.init_db:
    os.environ["INIT_DB"] = "true"

from app.core.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST, # 服务地址
        port=settings.PORT, # 端口号
        reload=settings.RELOAD, # 热重载
        log_level=settings.LOG_LEVEL, # 日志级别
    )