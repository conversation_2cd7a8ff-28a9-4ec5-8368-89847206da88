"""
backend/app/models/base.py
模型基类
"""

from sqlalchemy import Integer,DateTime
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func #用于生成时间戳

class TimestampMixin:
    """
    时间戳模型基类
    """
    created_at:Mapped[DateTime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False, 
        comment="创建时间"
        )
    updated_at:Mapped[DateTime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(),
        nullable=False, 
        onupdate=func.now(), # 更新时自动更新
        comment="更新时间"
        )

class AuditMixin:
    """
    审计模型基类
    """
    created_by:Mapped[int] = mapped_column(Integer,nullable=False, comment="创建人")
    updated_by:Mapped[int] = mapped_column(Integer,nullable=False, comment="更新人")

class BaseIDMixin:
    """
    主键模型基类
    """
    uid:Mapped[int] = mapped_column(Integer, primary_key=True, index=True, comment="主键")

class BaseModel(BaseIDMixin,TimestampMixin,AuditMixin):
    """
    模型基类
    """
    pass

class TimeStampedModel(BaseIDMixin,TimestampMixin):
    """
    时间戳模型基类
    """
    pass
