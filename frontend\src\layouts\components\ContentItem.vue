<!-- frontend/src/layouts/components/ContentItem.vue -->
 <!-- 主内容区 -->
<template>
        <n-layout-content content-style="padding: 24px; overflow:auto; ">
           <n-tabs
                type="card"
                v-model:value="activeTab"
                closable
                @close="handleClose"
                style="width: 100%; height: 100%;"
                >
                <n-tab-pane
                    v-for="tab in tabList"
                    :key="tab.path"
                    :name="tab.path"
                    :closable="!tab.affix"
                    :title="tab.title"
                    display-directive="show:lazy"
                >
                  <!-- 自定义 tab 标题 -->
                  <template #tab>
                    <div style=" overflow: hidden; height: 100%; width: 100%;">
                    <TabTitleDropdown :title="tab.title" :path="tab.path" />
                    </div>
                  </template>

                    <keep-alive>
                        <component :is="getComponent(tab.path)" />
                    </keep-alive>
                </n-tab-pane>

              </n-tabs>
        </n-layout-content>
</template>

<script lang="ts" setup>
import { useTabs } from '@/components/tab/useTabs'
import { useRouter } from 'vue-router'
import TabTitleDropdown from '@/components/tab/TabTitleDropdown.vue'
import { defineAsyncComponent, type Component } from 'vue'

const { tabList, activeTab, handleClose } = useTabs()
const router = useRouter()

function getComponent(path: string) {
  const matched = router.getRoutes().find(r => r.path === path)
  const raw = matched?.components?.default 
   if (!raw) {
    return () => '页面未找到'
  }
  // ✅ 如果是异步加载器（返回 Promise），用 defineAsyncComponent 包裹
  const isAsyncLoader = typeof raw === 'function' && raw.toString().includes('import(')
  if (isAsyncLoader) {
    return defineAsyncComponent(raw as () => Promise<Component>)
  }
  return raw
}
</script>



<style>

</style>
