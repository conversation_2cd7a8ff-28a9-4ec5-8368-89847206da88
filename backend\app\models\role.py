"""
backend/app/models/role.py
角色模型
"""

from sqlalchemy import String,<PERSON><PERSON>an
from sqlalchemy.orm import Mapped,mapped_column,relationship

from app.core.database import Base
from app.models.base import BaseIDMixin
from app.models.association import User_Role,Role_Menu


class Role(Base,BaseIDMixin):
    # 角色表
    __tablename__="roles"
    name:Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="角色名称")
    description:Mapped[str] = mapped_column(String(255), nullable=True, comment="角色描述")
    is_active:Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")  #可用状态，激活可使用，未激活不可使用
    is_default:Mapped[bool] = mapped_column(Boolean, default=False, comment="是否默认角色")  #默认角色，用户创建时会自动分配

    user_role:Mapped[list["User_Role"]]=relationship(back_populates="role", lazy="selectin") 
    menu_links:Mapped[list["Role_Menu"]]=relationship(back_populates="role", lazy="selectin")