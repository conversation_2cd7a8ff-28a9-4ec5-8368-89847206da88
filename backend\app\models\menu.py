"""
backend/app/models/menu.py
菜单模型
"""
import enum
from typing import TYPE_CHECKING,TypedDict
from sqlalchemy import String,<PERSON>te<PERSON>,<PERSON><PERSON>an,<PERSON>um,JSON,ForeignKey
from sqlalchemy.orm import Mapped,mapped_column,relationship

from app.core.database import Base
from app.models.base import BaseIDMixin
from app.models.association import Role_Menu
from app.models.data_permissions import OperationType
if TYPE_CHECKING:
    from app.models.module import Module
   

class MenuType(str,enum.Enum):
    menu="menu"
    button="button"

class MenuMeta(TypedDict,total=False):
    affix: bool         # 是否固定标签页
    cache: bool         # 是否缓存页面
    new_window: bool    # 是否新窗口打开
    hidden: bool        # 是否隐藏
    icon: str           # 图标
    title: str          # 菜单标题（可用于国际化）

class Menu(Base,BaseIDMixin):
    # 菜单表
    __tablename__="menus"
    module_id:Mapped[int] = mapped_column(ForeignKey("modules.uid", ondelete="SET NULL"),nullable=False,comment="模块ID")
    name:Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="菜单名称")
    parent_id:Mapped[int|None] = mapped_column(ForeignKey("menus.uid", ondelete="RESTRICT"),nullable=True,comment="父菜单ID")
    sort:Mapped[int] = mapped_column(Integer,nullable=True,default=0,comment="排序")
    #行为控制
    is_active:Mapped[bool] = mapped_column(Boolean,default=True,comment="激活状态")    

    # 路由信息
    path:Mapped[str] = mapped_column(String(255),default="",nullable=True,comment="路径")
    component:Mapped[str] = mapped_column(String(255),default="",nullable=True,comment="组件")
    redirect:Mapped[str] = mapped_column(String(255),default="",nullable=True,comment="重定向")
    #权限控制
    requires_auth:Mapped[bool] = mapped_column(Boolean,default=True,comment="是否需要权限")
    menu_type: Mapped[MenuType] = mapped_column(Enum(MenuType,native_enum=False), default=MenuType.menu, comment="菜单类型")
    operation:Mapped[OperationType| None] = mapped_column(Enum(OperationType,native_enum=False),nullable=True, comment="操作类型，仅用于按钮菜单") #用于区分这个按钮是什么操作类型的按钮，方便权限控制
    #元数据
    meta:Mapped[MenuMeta]=mapped_column(JSON,nullable=True,comment="元数据")
    
    
    children: Mapped[list["Menu"]] = relationship(back_populates="parent", cascade="all, delete-orphan") # 子菜单
    parent:Mapped["Menu"]=relationship(back_populates="children",remote_side=lambda: [Menu.uid]) # 父菜单 使用lambda延迟导入

    module:Mapped["Module"]=relationship(back_populates="menus") # 模块
    role_links:Mapped[list["Role_Menu"]]=relationship(back_populates="menu", lazy="selectin") # 增加lazy=selectin 一次性查询 避免N+1
