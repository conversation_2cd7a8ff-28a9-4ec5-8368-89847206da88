"""
backend/app/core/response/exceptions.py
自定义业务异常类
"""
from typing import Optional

class BizException(Exception):
    """业务异常基类"""

    def __init__(
        self,
        message: str = "业务处理失败",
        code: int = 400,
        error_detail: Optional[str] = None,
        silent: bool = False
    ):
        """
        初始化业务异常

        Args:
            message: 错误消息
            code: 错误代码
            error_detail: 详细错误信息
            silent: 是否静默处理（不包装响应）
        """
        self.message = message
        self.code = code
        self.error_detail = error_detail
        self.silent = silent
        super().__init__(message)

class ValidationException(BizException):
    """参数验证异常"""

    def __init__(self, message: str = "参数验证失败", **kwargs):
        super().__init__(message, code=422, **kwargs)

class AuthenticationException(BizException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(message, code=401, **kwargs)

class AuthorizationException(BizException):
    """授权异常"""

    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(message, code=403, **kwargs)

class NotFoundException(BizException):
    """资源不存在异常"""

    def __init__(self, message: str = "资源不存在", **kwargs):
        super().__init__(message, code=404, **kwargs)

class ConflictException(BizException):
    """资源冲突异常"""

    def __init__(self, message: str = "资源冲突", **kwargs):
        super().__init__(message, code=409, **kwargs)

class RateLimitException(BizException):
    """频率限制异常"""

    def __init__(self, message: str = "请求过于频繁", **kwargs):
        super().__init__(message, code=429, **kwargs)