// src/router/dynamic/register.ts
import type { Router } from 'vue-router'
import type { LoginResponseMenu } from '@/types/menu'
import { flattenMenusToRoutes } from './transform'

export async function registerDynamicRoutes(router: Router, menus: LoginResponseMenu[]): Promise<boolean> {
  try {
      router.addRoute('layout', {
        path: '/',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: true,
          affix: true
        }
      })
      console.log('✅ 注册默认首页路由')
    const dynamicRoutes = flattenMenusToRoutes(menus)
    dynamicRoutes.forEach(route => {
      if (route.path==="/") return

      const relativePath  =route.path.replace(/^\/+/, '')
      // 检查是否已注册为 layout 的子路由
      const layoutRoute = router.getRoutes().find(r => r.name === 'layout')
      const alreadyExists = layoutRoute?.children?.some(child => child.path === relativePath)
      if (alreadyExists) return
      router.addRoute('layout',{
        ...route,
        path: relativePath
      })
      console.log(`✅ 注册动态路由: ${relativePath}`)
    })
    console.log(`🎯 共注册动态路由数量: ${dynamicRoutes.length}`)
    return true
  } catch (err) {
    console.error('❌ 动态路由注册失败:', err)
    return false
  }
}
