"""
backend/app/models/department.py
部门模型
"""
from typing import TYPE_CHECKING
from sqlalchemy import String,Integer,<PERSON><PERSON>an,ForeignKey
from sqlalchemy.orm import Mapped,mapped_column,relationship

from app.core.database import Base
from app.models.base import TimeStampedModel

if TYPE_CHECKING:
    from app.models.user import User

class Department(Base,TimeStampedModel):
    # 部门表
    __tablename__="departments"

    name:Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="部门名称")
    parent_id:Mapped[int] = mapped_column(Integer,ForeignKey("departments.uid"),nullable=True,comment="父部门ID")
    sort:Mapped[int] = mapped_column(Integer,nullable=True,comment="排序")
    is_active:Mapped[bool] = mapped_column(Boolean,default=True,comment="激活状态")

    users:Mapped[list["User"]]=relationship(back_populates="department",lazy="selectin")
    
