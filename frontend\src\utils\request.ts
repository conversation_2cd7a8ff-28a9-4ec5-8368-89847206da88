/**
 * frontend/src/utils/request.ts
 * 网络请求工具
 */

import axios from 'axios'
import type {AxiosRequestConfig, AxiosResponse } from 'axios'

import { useUserStore } from '@/stores/user'
import router from '@/router'

import type { ApiResponse } from '@/types/request'
import { extractErrorMessage } from '@/utils/error'

import { eventBus } from '@/utils/eventBus'


const service=axios.create({
    baseURL:import.meta.env.VITE_API_BASE_URL,
    timeout:5000,
})

// 请求拦截器 自动注入 token
service.interceptors.request.use(config=>{
    const store=useUserStore()
    const token=store.token
    if(token){
        config.headers?.set('Authorization', `Bearer ${token}`)
    }
    return config
})

// 响应拦截器 统一处理错误
service.interceptors.response.use(
     (response: AxiosResponse) => {
    const res = response.data
    if(res && typeof res=="object"&& ('success' in res || 'error' in res)){
      if (res.success === false || res.error) {
        const msg = extractErrorMessage(res)
        eventBus.emit('notify:error',msg)
        console.error(msg)
        if (res.code === 401) {
          useUserStore().logout()
          router.push({ name: 'login' })
        }
        return Promise.reject(res)
      }
      return res
    }
    return response.data
  },  
    (error) => {
        const raw = error.response?.data ?? error
        const msg = extractErrorMessage(raw)
        eventBus.emit('notify:error',msg)

        if (error.response?.status === 401) {
        useUserStore().logout()
        router.push({ name: 'login' })
        }

        return Promise.reject(error)
    }
)

export default service

// 通用请求函数
async function request<T = unknown>(config: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<ApiResponse<T>> = await service.request(config)
  const res = response.data
  if (res && typeof res == 'object' && 'success' in res){
    if (!res.success || res.error) {
      const msg = extractErrorMessage(res)
      eventBus.emit('notify:error',msg)
      throw new Error(msg)
    }
    return res.data as T
  }
  return res as T
}

// 方法别名封装
export function get<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return request<T>({ ...config, method: 'GET', url })
}

export function post<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
  return request<T>({ ...config, method: 'POST', url, data })
}

export function put<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
  return request<T>({ ...config, method: 'PUT', url, data })
}

export function del<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return request<T>({ ...config, method: 'DELETE', url })
}