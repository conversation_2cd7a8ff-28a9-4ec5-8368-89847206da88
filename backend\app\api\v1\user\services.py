"""
backend/app/api/v1/user/services.py
用户业务逻辑
"""

from app.models.user import User

from app.core.database import transactional_session
from app.schemas.auth import AuthUser
from app.api.v1.user.schemas import UserInfo

#获取用户登录必要信息
def get_AuthUser(user:User)->AuthUser:
    return AuthUser.model_validate(user,from_attributes=True) #通过 from_attributes=True 从数据库模型转换为Pydantic模式，schemas 和 models 对应可以直接映射。

# 根据ID 获取用户个人信息
def get_user_info(id:int)->UserInfo:
    with transactional_session() as db:
        user=db.query(User).filter(User.uid==id).first()
        if not user:
            raise ValueError("用户不存在")        
        return UserInfo.from_user(user) 

