<!-- frontend/src/layouts/common/SplitViewLayout.vue -->
<template>
  <n-layout has-sider style="height: 100%;">
    <!-- 左侧区域 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :width="leftWidth"
      style="padding: 16px; overflow: auto;"
    >
      <slot name="left" />
    </n-layout-sider>

    <!-- 右侧区域 -->
    <n-layout-content style="padding: 16px; overflow: auto;">
      <slot name="right" />
    </n-layout-content>
  </n-layout>
</template>

<script lang="ts" setup>
defineProps<{
  leftWidth?: number
}>()
</script>
