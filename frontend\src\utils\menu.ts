// frontend/src/utils/menu.ts
import { h, defineAsyncComponent } from 'vue'
import { NIcon } from 'naive-ui'
import type { MenuOption } from 'naive-ui'
import type { Component } from 'vue'

import type { LoginResponseMenu } from '@/types/menu'

//图标 映射关系
function resolveIconComponent(name: string) {
  const iconMap: Record<string, () => Promise<Component>> = {
    Home: () => import('@vicons/ionicons5/HomeOutline'),
    Setting: () => import('@vicons/ionicons5/SettingsOutline'),
    User: () => import('@vicons/ionicons5/PersonOutline'),
    Role: () => import('@vicons/ionicons5/PeopleOutline'),
    Department: () => import('@vicons/ionicons5/BusinessOutline'),
    Menu: () => import('@vicons/ionicons5/ListOutline'),
    Test: () => import('@vicons/ionicons5/FlaskOutline')
  }
  const loader = iconMap[name] || iconMap['Menu']
  return defineAsyncComponent(loader)
}

function renderIcon(name?: string | null) {
  if (!name) return undefined
  const IconComp = resolveIconComponent(name)
  return () => h(NIcon, null, { default: () => h(IconComp) })
}

export function transformMenusToOptions(menus: LoginResponseMenu[]): MenuOption[] {
  return menus
    .sort((a, b) => (a.sort ?? 0)-(b.sort ?? 0) )
    .map(menu => ({
      label: menu.meta?.title || menu.name,
      key: menu.path,
      icon: renderIcon(menu.meta?.icon),
      children: menu.children?.length ? transformMenusToOptions(menu.children) : undefined
    }))
}
