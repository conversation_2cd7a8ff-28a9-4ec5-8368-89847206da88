"""
backend/app/models/module.py
模块表格，用于管理菜单归属
"""
from typing import TYPE_CHECKING
from sqlalchemy import String,Integer,Bo<PERSON>an
from sqlalchemy.orm import Mapped,mapped_column,relationship

from app.core.database import Base
from app.models.base import BaseIDMixin

if TYPE_CHECKING:
    from app.models.menu import Menu

class Module(Base,BaseIDMixin):
    # 模块表
    __tablename__="modules"
    name:Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="模块名称")
    parent_id:Mapped[int] = mapped_column(Integer,nullable=True,comment="父模块ID")
    description:Mapped[str] = mapped_column(String(255), nullable=True, comment="模块描述")
    is_active:Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")  #可用状态，激活可使用，未激活不可使用

    menus:Mapped[list["Menu"]]=relationship(back_populates="module", lazy="selectin")