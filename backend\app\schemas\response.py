"""
backend/app/schemas/response.py
API 响应模型
"""
from pydantic import BaseModel,Field
from typing import Generic,TypeVar,Optional

T = TypeVar("T") # 用于定义泛型类型

class ApiResponse(BaseModel,Generic[T]):
    success: bool = Field(..., description="请求是否成功")
    data: Optional[T] = Field(None, description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")
    code: Optional[int] = Field(None, description="响应代码")
    error: Optional[str] = Field(None, description="错误信息")

def success_response(data: T, message: str = None, code: int = 200) -> ApiResponse[T]:
    return ApiResponse(success=True, data=data, message=message, code=code)
def error_response(message: str, code: int, error: Optional[str] = None) -> ApiResponse[None]:
    return ApiResponse(success=False, data=None, message=message, code=code, error=error)

