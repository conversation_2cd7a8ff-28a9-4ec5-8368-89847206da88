/**
 * frontend/src/stores/user.ts
 * 用户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { get } from '@/utils/request'
import { login as loginApi } from '@/api/user'
import type { TokenWithMenus, UserInfo, loginData } from '@/types/user'
import { transformMenusToOptions } from '@/utils/menu'
import { useTabStore } from './useTabStore'

export const useUserStore = defineStore('user', () => {
  // 登录后的认证信息（token、菜单、按钮权限、用户角色）
  const authPayload = ref<TokenWithMenus | null>(null)

  // 用户个人信息（用于个人主页）
  const userinfo = ref<UserInfo | null>(null)

  // token getter
  const token = computed(() => authPayload.value?.token ?? null)

  // 是否已登录
  const isLoggedIn = computed(() => !!token.value)

  // 菜单权限
  const menus = computed(() => authPayload.value?.menus ?? [])

  //菜单格式化
  const menuOptions=computed(() => transformMenusToOptions(authPayload.value?.menus ?? []))

  // 动态路由是否已初始化
  const dynamicRoutesInitialized = ref(false)
  // 按钮权限
  const buttons = computed(() => authPayload.value?.buttons ?? [])

  // 当前用户角色信息
  const currentUser = computed(() => authPayload.value?.user ?? null)

  // 登录方法：调用 API 并更新状态
  async function login(payload: loginData) {
    const res = await loginApi(payload)
    console.log("登录成功:",res)
    authPayload.value = res
    localStorage.setItem('authPayload', JSON.stringify(res))
  }

  // 获取当前用户信息（用于刷新或恢复状态）
  async function fetchUserInfo() {
    const res = await get<UserInfo>('/user/me')
    userinfo.value = res
    localStorage.setItem('userinfo', JSON.stringify(res))
  }

  // 恢复状态（页面刷新后）
  function restore() {
    const rawAuth = localStorage.getItem('authPayload')
    if (rawAuth) {
      authPayload.value = JSON.parse(rawAuth) as TokenWithMenus
    }

    const rawUser = localStorage.getItem('userinfo')
    if (rawUser) {
      userinfo.value = JSON.parse(rawUser) as UserInfo
    }
  }

  // 设置动态路由初始化状态
  function setDynamicRoutesInitialized(status: boolean) {
    dynamicRoutesInitialized.value = status
  }

  // 退出登录：清除状态和本地存储
  function logout() {
    authPayload.value = null
    userinfo.value = null
    dynamicRoutesInitialized.value = false
    localStorage.removeItem('authPayload')
    localStorage.removeItem('userinfo')
    useTabStore().resetTabs()
  }

  return {
    // 状态
    authPayload,
    userinfo,
    dynamicRoutesInitialized,
    // 计算属性
    token,
    isLoggedIn,
    menus,
    buttons,
    currentUser,
    menuOptions,

    // 方法
    login,
    fetchUserInfo,
    restore,
    logout,
    setDynamicRoutesInitialized,
  }
})