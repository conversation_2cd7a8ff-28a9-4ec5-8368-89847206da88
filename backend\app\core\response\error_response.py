"""
backend/app/core/response/error_response.py
响应模型：定义统一的响应结构
"""
from pydantic import BaseModel, Field
from typing import Generic, TypeVar, Optional

T = TypeVar("T")

class ApiResponse(BaseModel, Generic[T]):
    """统一API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    data: Optional[T] = Field(None, description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")
    code: Optional[int] = Field(None, description="响应代码")
    error: Optional[str] = Field(None, description="错误信息")

def success_response(data: T = None, message: str = "请求成功", code: int = 200) -> ApiResponse[T]:
    """创建成功响应"""
    return ApiResponse(success=True, data=data, message=message, code=code)

def error_response(message: str, code: int = 500, error: Optional[str] = None) -> ApiResponse[None]:
    """创建错误响应"""
    return ApiResponse(success=False, data=None, message=message, code=code, error=error)
