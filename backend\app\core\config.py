"""
backend/app/core/config.py
应用配置管理
"""

from pydantic_settings import BaseSettings

class Settings(BaseSettings):

    class config:
        env_file = ".env" # 读取环境变量的文件
        env_file_encoding = "utf-8" # 环境变量文件的编码格式
        case_sensitive = True # 是否区分大小写
        

    #应用配置
    PROJECT_NAME: str = "FastAPI" # 项目名称
    PROJECT_VERSION: str = "1.0.0" # 项目版本
    PROJECT_DESCRIPTION: str = "FastAPI项目" # 项目描述
    DEBUG: bool = True # 是否调试模式
    
    # 服务配置
    HOST: str = "0.0.0.0" #服务地址
    PORT: int = 8000 #服务端口
    RELOAD: bool = True # 是否热重载
    LOG_LEVEL: str = "info" #"info" "warning" # 日志级别

    # 数据库配置
    DB_TPYE: str = "sqlite" # "mysql" | "sqlite" # 数据库类型
    DB_HOST: str = "localhost" # 数据库地址
    DB_PORT: int = 3306 # 数据库端口
    DB_USER: str = "root" # 数据库用户名
    DB_PASSWORD: str = "root" # 数据库密码
    DB_NAME: str = "fastapi" # 数据库名称
    
    DB_SQLITE_PATH: str = "sqlite:///./fastapi.db" # sqlite数据库路径
    DB_SQLITE_CONNECT_ARGS: dict = {"check_same_thread": False} # sqlite数据库连接参数



    # JWT配置
    JWT_SECRET_KEY: str = "thisisaverysecretkeythatshouldbe32characterslongorlonger" # JWT密钥
    JWT_ALGORITHM: str = "HS256" # JWT算法
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 # JWT过期时间
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int =  7 # JWT刷新令牌过期天数

    # CORS配置
    CORS_ORIGINS: list = ["*"] # CORS允许的源
    CORS_ALLOW_CREDENTIALS: bool = True # CORS是否允许携带cookie
    CORS_ALLOW_METHODS: list = ["*"] # CORS允许的方法
    CORS_ALLOW_HEADERS: list = ["*"] # CORS允许的头

    # API配置
    API_PREFIX: str = "/api" # API前缀
    API_V1_STR: str = "/v1" # API版本

    # 初始化数据库设置
    INIT_DB: bool = False # 是否初始化数据库

settings = Settings()
