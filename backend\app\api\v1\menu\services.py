"""
backend/app/api/v1/menu/services.py
菜单业务逻辑
"""

from app.models.menu import Menu
from app.models.user import User
from app.schemas.auth import LoginResponseMenu,ButtonPermission
from app.models.menu import MenuType

#用于输出给前端登录使用
def get_user_access_data(user:User)->tuple[list[LoginResponseMenu], list[ButtonPermission]]:

    roles=[ur.role for ur in user.user_role]
    menus:dict[int, Menu] = {}
    buttons:list[ButtonPermission]=[]

    # 遍历父菜单，兜底策略
    def collect_menu(menu: Menu):
        if menu.uid in menus or not menu.is_active:
            return
        menus[menu.uid] = menu 
        menu.children=[]
        if menu.parent and menu.parent.is_active:
            collect_menu(menu.parent)

    # 循环遍历 分别取出 菜单和按钮
    for role in roles:
        for link in role.menu_links: 
            menu=link.menu
            if menu.is_active and menu.module and menu.module.is_active:
                if menu.menu_type==MenuType.button:
                    buttons.append(ButtonPermission(uid=menu.uid,name=menu.name,operation=menu.operation.value))
                else:
                    collect_menu(menu)

   
    # 构建树形结构
    for menu in menus.values():
        if menu.parent_id and menu.parent_id in menus:
            parent = menus[menu.parent_id]
            parent.children.append(menu)
    
    # 对每个菜单的 children排序 , reverse=True 降序， 默认升序
    for menu in menus.values():
        menu.children.sort(key=lambda x: x.sort)
    # 对 顶级菜单排序
    top_level_menus = sorted(
        [menu for menu in menus.values() if not menu.parent_id or menu.parent_id not in menus],
        key=lambda x: x.sort
        )

    # 构建响应数据
    top_level_menus = [menu for menu in menus.values() if not menu.parent_id or menu.parent_id not in menus]
    menu_response_menus=[to_login_response_menu(menu) for menu in top_level_menus]

    return menu_response_menus,buttons


# 递归函数 构建所需数据
def to_login_response_menu(menu: Menu) -> LoginResponseMenu:
    return LoginResponseMenu(
        uid=menu.uid,
        name=menu.name,
        path=menu.path,
        sort=menu.sort,
        component=menu.component,
        redirect=menu.redirect,
        meta=menu.meta,
        requires_auth=menu.requires_auth,
        children=[to_login_response_menu(child) for child in menu.children] if menu.children else []
    )


#用于菜单的联级更新, 保持父菜单被禁用了子菜单也禁用
def cascade_update_menu(menu:Menu,is_active:bool):
    if is_active: 
        if not menu.is_active:
            menu.is_active=True
        enable_parent_chain(menu)
    else:
        disable_menu_tree(menu)

# 联级更新  父禁用->子禁用
def disable_menu_tree(menu:Menu):
    if not menu.is_active:
        return
    menu.is_active=False
    for child in menu.children:
        disable_menu_tree(child)

# 联级更新 子启用->父启用
def enable_parent_chain(menu: Menu):
    parent = menu.parent
    if not parent:
        return
    if not parent.is_active:
        parent.is_active = True
        enable_parent_chain(parent)