/**
 * frontend/src/router/guards.ts
 * 路由守卫
 */

import type { Router } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { registerDynamicRoutes } from './dynamic/index'

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()

    console.log('路由守卫触发:', {
      path: to.path,
      name: to.name,
      isLoggedIn: userStore.isLoggedIn,
      dynamicRoutesInitialized: userStore.dynamicRoutesInitialized,
      requiresAuth: to.meta.requiresAuth
    })

    // 登录页直接放行
    if (to.name === 'login') {
      console.log('放行登录页')
      next()
      return
    }

    // 如果是404页面但用户已登录且动态路由未初始化，可能是直接访问动态路由
    if (to.name === 'not-found' && userStore.isLoggedIn && !userStore.dynamicRoutesInitialized) {
      console.log('404页面但用户已登录，尝试注册动态路由')
      try {
        const success = await registerDynamicRoutes(router, userStore.menus)
        if (success) {
          userStore.setDynamicRoutesInitialized(true)
          console.log('动态路由注册成功，重新解析路由:', to.path)
          // 重新解析当前路径
          next({ path: to.path, replace: true })
          return
        }
      } catch (error) {
        console.error('动态路由注册异常:', error)
      }
    }

    // 如果是404页面且动态路由已初始化，检查是否有匹配的动态路由
    if (to.name === 'not-found' && userStore.dynamicRoutesInitialized) {
      console.log('404页面但动态路由已初始化，检查是否有匹配的动态路由')
      const allRoutes = router.getRoutes()
      console.log('当前所有路由:', allRoutes.map(r => ({ name: r.name, path: r.path })))

      // 尝试通过路径匹配找到正确的路由
      const matchingRoute = allRoutes.find(route =>
        route.path === to.path && route.name !== 'not-found'
      )

      if (matchingRoute) {
        console.log('找到匹配路由，重新导航:', matchingRoute.name)
        next({ name: matchingRoute.name as string, replace: true })
        return
      } else {
        console.log('确实没有找到匹配的路由，显示404')
        next()
        return
      }
    }
    // 404页面正常放行
    if (to.name === 'not-found') {
      console.log('放行404页')
      next()
      return
    }

    // 如果用户未登录，跳转到登录页
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，跳转到登录页')
      next({ name: 'login', query: { redirect: to.fullPath }, replace: true })
      return
    }

    // 用户已登录但动态路由未初始化，先注册动态路由
    if (!userStore.dynamicRoutesInitialized) {
      try {
        console.log('路由守卫中注册动态路由...')
        const success = await registerDynamicRoutes(router, userStore.menus)

        if (success) {
          userStore.setDynamicRoutesInitialized(true)
          console.log('动态路由注册成功，重新导航到:', to.fullPath)
          // 重新导航到目标路由，确保路由匹配成功
          if(to.name==='layout'){
            next({ name: 'home', replace: true })
            return
          }
          next({ path: to.path, replace: true })
        } else {
          console.error('动态路由注册失败')
          next({ name: 'login', replace: true })
        }
        return
      } catch (error) {
        console.error('动态路由注册异常:', error)
        next({ name: 'login', replace: true })
        return
      }
    }

    if (to.name === 'layout' && userStore.dynamicRoutesInitialized) {
      console.log('首次跳转到 layout，重定向到 home')
      next({ name: 'home', replace: true })
      return
    }
    // 正常放行
    console.log('正常放行:', to.path)
    next()
  })
}



  //每次路由跳转前都会执行。它接收三个参数：
  // to: 即将要进入的目标路由对象
  // from: 当前导航正要离开的路由
  // next: 一定要调用该方法来 resolve 这个钩子。执行效果依赖 next 方法的调用参数。
