"""
backend/app/core/response/exception_handlers.py
异常处理器：捕获并格式化各种异常
"""
import traceback
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as Starlette<PERSON>TPException

from .error_response import error_response
from .exceptions import BizException
from app.core.config import settings

def biz_exception_handler(request: Request, exc: BizException) -> JSONResponse:
    """处理业务异常"""
    response_data = error_response(
        message=exc.message,
        code=exc.code,
        error=exc.error_detail if settings.DEBUG else None
    )
    return J<PERSON>NResponse(
        status_code=exc.code,
        content=response_data.model_dump()
    )


def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """处理HTTP异常"""
    response_data = error_response(
        message=exc.detail,
        code=exc.status_code,
        error=exc.__class__.__name__ if settings.DEBUG else None
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data.model_dump()
    )


def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """处理参数验证异常"""
    errors = []
    for error in exc.errors():
        errors.append({
            "loc": error.get("loc"),
            "msg": error.get("msg"),
            "type": error.get("type")
        })

    response_data = error_response(
        message="参数验证失败",
        code=422,
        error=str(errors) if settings.DEBUG else "参数验证失败"
    )
    return JSONResponse(
        status_code=422,
        content=response_data.model_dump()
    )


def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理通用异常"""
    # 在开发环境中提供详细的错误信息
    error_detail = None
    if settings.DEBUG:
        error_detail = {
            "traceback": traceback.format_exc(),
            "exception": str(exc)
        }

    response_data = error_response(
        message="服务器内部错误",
        code=500,
        error=str(error_detail) if settings.DEBUG else None
    )
    return JSONResponse(
        status_code=500,
        content=response_data.model_dump()
    )


def register_exception_handlers(app):
    """注册所有异常处理器"""
    app.add_exception_handler(BizException, biz_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, generic_exception_handler)