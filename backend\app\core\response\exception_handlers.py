"""
backend/app/core/exception_handlers.py
异常处理程序
"""
import traceback
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.schemas.response import error_response
from app.core.config import settings

def http_exception_handler(request: Request, exc: StarletteHTTPException):
    response_data=error_response(
            message=exc.detail,
            code=exc.status_code,
            error=exc.__class__.__name__
        )
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data.model_dump()
        )
    

def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors=[]
    for error in exc.errors():
        errors.append({
            "loc": error.get("loc"),
            "msg": error.get("msg"),
            "type": error.get("type")
        })
    response_data=error_response(
            message="参数验证失败",
            code=422,
            error=str(errors) if settings.DEBUG else "参数验证失败"
        )
    return J<PERSON>NR<PERSON>ponse(
        status_code=422,
        content=response_data.model_dump()
        )

def generic_exception_handler(request: Request, exc: Exception):
    #在开发环境中提供详细的错误信息
    error_detail=None
    if settings.DEBUG:
        error_detail={
            "traceback": traceback.format_exc(),
            "exception": str(exc)
        }
    response_data=error_response(   
            message="服务器内部错误",
            code=500,
            error=exc.__class__.__name__ if not settings.DEBUG else str(error_detail)
        )
    return JSONResponse(
        status_code=500,
        content=response_data.model_dump()
    )