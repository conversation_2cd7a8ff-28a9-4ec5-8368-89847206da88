// frontend/src/stores/useTabs.ts
import { computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import type { LoginResponseMenu } from '@/types/menu'
import { useTabStore } from '@/stores/useTabStore'

const tabStore = useTabStore()
const tabList = computed(() => tabStore.tabList)
const activeTab = computed({
  get: () => tabStore.activeTab,
  set: (val: string) => tabStore.setActiveTab(val)
})


let initialized=false

export function useTabs() {

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()

  tabStore.setActiveTab(route.path)

  // 递归查找菜单 
  function findMenuByPath(menus: LoginResponseMenu[], path: string): LoginResponseMenu | undefined {
    for (const menu of menus) {
      if (menu.path === path) return menu
      if (menu.children) {
        const found = findMenuByPath(menu.children, path)
        if (found) return found
      }
    }
    return undefined
  }
  // 标准化路径
  function normalizePath(path: string): string {
    const cleaned = path.replace(/\/+$/, '')
    console.log("原数据",path,"标准化后:",cleaned)
    return cleaned === '' ? '/' : cleaned
  }

  // 初始化固定标签页
  function initAffixTabs() {
    if (initialized) return
    initialized = true
    const affixMenus = userStore.menus.filter(menu => menu.meta?.affix)
    affixMenus.forEach(menu => {
      tabStore.addTab({
        title: menu.meta?.title || menu.name,
        path: menu.path,
        affix: true
      })
    })
  }

 function handleClose(path: string) {  
    const index = tabList.value.findIndex(tab => tab.path === path)
    if (index !== -1 && !tabList.value[index].affix) {
      const isActive = activeTab.value === path
      // ✅ 先计算跳转目标
      const nextTab =
        tabList.value[index + 1] || // 优先跳转到右侧标签
        tabList.value[index - 1] || // 否则跳转到左侧标签
        tabList.value[0]            // 最后兜底跳转第一个标签

      tabList.value.splice(index, 1)
     // ✅ 无论是否是当前激活页，都强制激活剩下的标签
      if (isActive || nextTab) {
          tabStore.setActiveTab(nextTab.path) 
          router.push(nextTab.path)
      }
    }
  }
  // 关闭左侧标签
  function closeLeft(currentPath: string) {
    tabStore.closeLeft(currentPath)
  }
  // 关闭右侧标签
  function closeRight(currentPath: string) {
    tabStore.closeRight(currentPath)
  }
  // 关闭其他标签
  function closeOthers(currentPath: string) {
    tabStore.closeOthers(currentPath)
  }
  // 关闭 全部
  function closeAll() {
    tabStore.closeAll()
    if (tabStore.tabList.length > 0) {
      router.push(tabStore.tabList[0].path)
    }
  }

  // 监听标签页变化
  watch(
    () => tabList.value.length,
    (len) => {
      console.log('标签页变化:', len)
      if (len === 0) {
        // 如果没有标签页，恢复 affix 标签
        initAffixTabs()
        if (tabList.value.length > 0) {
          tabStore.setActiveTab(tabList.value[0].path)
          router.push(activeTab.value)
        }
      } else {
        // 如果当前 activeTab 不在 tabList 中，修正它
        const exists = tabList.value.some(tab => tab.path === activeTab.value)
        if(!exists && tabList.value.length > 0){
          tabStore.setActiveTab(tabList.value[0].path)
          router.push(activeTab.value)
        }
      }
    }
  )

  // 监听路由变化
  watch(
    () => route.fullPath,
    (newPath) => {
      const normalized=normalizePath(newPath)    
      if (activeTab.value===normalized) return
      
      tabStore.setActiveTab(normalized)

      const matchedMenu = findMenuByPath(userStore.menus, normalized)
      if (!matchedMenu) return // ⛔️ 没有匹配菜单就不生成标签页    

      const exists = tabList.value.some(tab => normalizePath(tab.path) === normalized)
       console.log('当前路径:', route.path, '标准化后:', normalizePath(route.path))
      if (!exists) {
        tabStore.addTab({
          title: matchedMenu?.meta?.title || matchedMenu?.name ,
          path: normalized,
          affix: matchedMenu?.meta?.affix
        })      
      }
    },
    { immediate: true }
  )

  initAffixTabs()

  return {
    tabList,
    activeTab,
    handleClose,
    closeLeft,
    closeRight,
    closeOthers,
    closeAll
  }
}
