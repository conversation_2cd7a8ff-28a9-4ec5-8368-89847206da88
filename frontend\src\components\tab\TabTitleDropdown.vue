<!-- frontend/src/components/tab/TabTitleDropdown.vue -->
 <!-- 标签页标题右键菜单 -->
<template>
      <div 
      ref="triggerRef" 
      @contextmenu.prevent="handleContextMenu"
      style="display: flex; align-items: center; gap: 4px; cursor: context-menu;">
      <span>{{ title }}</span>
      <n-icon size="14"><i class="i-carbon-overflow-menu-horizontal" /></n-icon>
        <n-dropdown
          :show="showDropdown"
          :x="dropdownX"
          :y="dropdownY"
          :options="dropdownOptions"
          @select="handleTabAction"
          @clickoutside="showDropdown = false"
        >
        </n-dropdown>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useTabs } from '@/components/tab/useTabs'

const props = defineProps<{
  title: string
  path: string
}>()

const { closeLeft, closeRight, closeOthers, closeAll } = useTabs()

const showDropdown = ref(false)
const dropdownX = ref(0)
const dropdownY = ref(0)
const triggerRef = ref<HTMLElement  | null>(null)

function handleContextMenu(event: MouseEvent) {
  event.preventDefault()
  dropdownX.value = event.clientX
  dropdownY.value = event.clientY
  showDropdown.value = true
}

const dropdownOptions = [
  { label: '关闭左侧', key: 'leftTab' },
  { label: '关闭右侧', key: 'rightTab' },
  { label: '关闭其他', key: 'closeOthers' },
  { label: '关闭所有', key: 'closeAll' }
]

function handleTabAction(key: string) {
  const currentPath = props.path
  switch (key) {
    case 'leftTab':
      closeLeft(currentPath)
      break
    case 'rightTab':
      closeRight(currentPath)
      break
    case 'closeOthers':
      closeOthers(currentPath)
      break
    case 'closeAll':
      closeAll()
      break
  }
}


</script>



