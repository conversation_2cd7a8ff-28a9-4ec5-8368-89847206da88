"""
backend/app/api/v1/auth/dependencies.py
认证相关依赖项 
认证服务层抽象（认证逻辑与模型解耦）
"""

from fastapi.security import HTT<PERSON><PERSON>earer,HTTPAuthorizationCredentials
from fastapi import Depends,HTTPException

from app.core.database import get_db
from app.schemas.auth import AuthUser
from app.core.security import verify_token
from app.models.user import User

security=HTTPBearer()

# 认证用户
async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db=Depends(get_db)
)->AuthUser:
   # HTTPAuthorizationCredentials 类型，包含两个字段：
    # scheme: "Bearer"
    # credentials: 实际的 JWT 字符串
   token= credentials.credentials #从请求头中提取出 JWT token。
   uid=verify_token(token)
   if uid is None:
    raise HTTPException(status_code=401, detail="无效令牌")
   
   user=db.query(User).filter(User.uid==uid).first()

   if not user:
    raise HTTPException(status_code=401, detail="用户不存在")
   
   return AuthUser.model_validate(user,from_attributes=True)

# 获取活跃用户
async def get_active_user(current_user: AuthUser = Depends(get_current_user))->AuthUser:
   if not current_user.is_active:
    raise HTTPException(status_code=401, detail="用户未激活")
   return current_user

# 获取超级用户
async def get_superuser(current_user: AuthUser = Depends(get_active_user))->AuthUser:
   if not current_user.is_superuser:
    raise HTTPException(status_code=401, detail="无权限")
