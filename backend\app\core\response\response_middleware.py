"""
backend/app/core/response/response_middleware.py
响应中间件：自动包装所有响应为标准结构
"""
import json
from typing import Callable

from fastapi import Request, Response
from fastapi.responses import JSONResponse, StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .error_response import success_response


class ResponseMiddleware(BaseHTTPMiddleware):
    """响应包装中间件"""

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: list[str] = None,
        exclude_methods: list[str] = None
    ):
        """
        初始化响应中间件

        Args:
            app: ASGI应用
            exclude_paths: 排除的路径列表（不进行包装）
            exclude_methods: 排除的HTTP方法列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json", "/favicon.ico",
            "/static", "/ws", "/websocket"  # 排除文档、静态文件、WebSocket
        ]
        # 确保包含OpenAPI相关路径---阿里ai修复内容：
        # if "/openapi.json" not in self.exclude_paths:
        #     self.exclude_paths.append("/openapi.json")
        self.exclude_methods = exclude_methods or ["OPTIONS", "HEAD"]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""

        # 检查是否需要跳过包装
        if self._should_skip_wrapping(request):
            return await call_next(request)

        # 执行请求
        response = await call_next(request)

        # 只处理JSON响应
        if not self._is_json_response(response):
            return response

        # 直接包装响应，不再检查是否已包装（避免重复读取body）
        return await self._wrap_response(response)

    def _should_skip_wrapping(self, request: Request) -> bool:
        """判断是否应该跳过包装"""
        # 检查路径
        for path in self.exclude_paths:
            if request.url.path.startswith(path):
                return True

        # 检查方法
        if request.method in self.exclude_methods:
            return True

        return False

    def _is_json_response(self, response: Response) -> bool:
        """判断是否为JSON响应"""
        # 跳过流式响应和其他特殊响应类型
        if isinstance(response, (StreamingResponse,)):
            return False

        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type

    async def _get_response_body(self, response: Response) -> bytes:
        """安全地获取响应体"""
        try:
            # 首先尝试使用 FastAPI >= 0.110 推荐的方式
            if hasattr(response, 'body') and callable(response.body):
                try:
                    return await response.body()
                except Exception:
                    pass

            # 如果有 _content 属性且不为空，直接使用
            if hasattr(response, '_content') and response._content:
                return response._content

            # 尝试从 body_iterator 读取（只读取一次）
            if hasattr(response, 'body_iterator'):
                body = b""
                try:
                    async for chunk in response.body_iterator:
                        body += chunk
                    # 保存到 _content 以便后续使用
                    if hasattr(response, '_content'):
                        response._content = body
                    return body
                except Exception:
                    pass

            return b""
        except Exception:
            return b""



    async def _wrap_response(self, response: Response) -> Response:
        """包装响应为标准格式"""
        try:
            # 跳过流式响应和其他特殊响应类型
            if isinstance(response, (StreamingResponse,)):
                return response

            # 获取原始响应体
            body = await self._get_response_body(response)

            # 解析响应数据
            if body:
                try:
                    data = json.loads(body.decode())
                    # 检查是否已经是标准格式
                    if isinstance(data, dict) and "success" in data:
                        # 已经是标准格式，直接返回
                        return response
                except json.JSONDecodeError:
                    data = body.decode()
            else:
                data = None

            # 创建标准响应
            wrapped_response = success_response(
                data=data,
                message="请求成功",
                code=response.status_code
            )

            return JSONResponse(
                status_code=response.status_code,
                content=wrapped_response.model_dump(),
                headers=dict(response.headers)
            )

        except Exception as e:
            # 如果包装失败，返回原响应
            print(f"响应包装失败: {e}")
            return response
        