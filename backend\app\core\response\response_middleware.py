"""
backend/app/core/response/response_middleware.py
响应中间件：自动包装所有响应为标准结构
"""
import json
from typing import Callable

from fastapi import Request, Response
from fastapi.responses import JSONResponse, StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .error_response import success_response


class ResponseMiddleware(BaseHTTPMiddleware):
    """响应包装中间件"""

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: list[str] = None,
        exclude_methods: list[str] = None
    ):
        """
        初始化响应中间件

        Args:
            app: ASGI应用
            exclude_paths: 排除的路径列表（不进行包装）
            exclude_methods: 排除的HTTP方法列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json", "/favicon.ico",
            "/static", "/ws", "/websocket"  # 排除文档、静态文件、WebSocket
        ]
        # 确保包含OpenAPI相关路径---阿里ai修复内容：
        # if "/openapi.json" not in self.exclude_paths:
        #     self.exclude_paths.append("/openapi.json")
        self.exclude_methods = exclude_methods or ["OPTIONS", "HEAD"]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""

        # 检查是否需要跳过包装
        if self._should_skip_wrapping(request):
            return await call_next(request)

        # 执行请求
        response = await call_next(request)

        # 只处理JSON响应
        if not self._is_json_response(response):
            return response

        # 检查响应是否已经是标准格式
        if await self._is_already_wrapped(response):
            return response

        # 包装响应
        return await self._wrap_response(response)

    def _should_skip_wrapping(self, request: Request) -> bool:
        """判断是否应该跳过包装"""
        # 检查路径
        for path in self.exclude_paths:
            if request.url.path.startswith(path):
                return True

        # 检查方法
        if request.method in self.exclude_methods:
            return True

        return False

    def _is_json_response(self, response: Response) -> bool:
        """判断是否为JSON响应"""
        # 跳过流式响应和其他特殊响应类型
        if isinstance(response, (StreamingResponse,)):
            return False

        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type

    async def _get_response_body(self, response: Response) -> bytes:
        """安全地获取响应体"""
        try:
            # 对于不同类型的响应，使用不同的方法获取body
            if hasattr(response, 'body') and callable(response.body):
                # FastAPI >= 0.110 推荐的方式
                body = await response.body()
                print(f"通过 body() 方法获取到: {body}")
                return body
            elif hasattr(response, '_content') and response._content:
                # 直接访问内容
                print(f"通过 _content 获取到: {response._content}")
                return response._content
            elif hasattr(response, 'body_iterator'):
                # 从迭代器读取
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                print(f"通过 body_iterator 获取到: {body}")
                return body
            else:
                print("无法获取响应体，返回空字节")
                return b""
        except Exception as e:
            print(f"获取响应体时出错: {e}")
            return b""

    async def _is_already_wrapped(self, response: Response) -> bool:
        """检查响应是否已经被包装"""
        try:
            # 跳过流式响应
            if isinstance(response, (StreamingResponse,)):
                return False

            # 获取响应体
            body = await self._get_response_body(response)

            if not body:
                return False

            # 尝试解析JSON
            data = json.loads(body.decode())

            # 检查是否包含标准字段
            if isinstance(data, dict) and "success" in data:
                return True

        except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
            pass

        return False

    async def _wrap_response(self, response: Response) -> Response:
        """包装响应为标准格式"""
        try:
            # 跳过流式响应和其他特殊响应类型
            if isinstance(response, (StreamingResponse,)):
                print("跳过流式响应")
                return response

            print(f"开始包装响应，类型: {type(response)}")

            # 获取原始响应体
            body = await self._get_response_body(response)
            print(f"获取到响应体: {body}")

            # 解析响应数据
            if body:
                try:
                    data = json.loads(body.decode())
                    print(f"解析JSON成功: {data}")
                except json.JSONDecodeError as e:
                    data = body.decode()
                    print(f"JSON解析失败，使用字符串: {data}, 错误: {e}")
            else:
                data = None
                print("响应体为空，data设为None")

            # 创建标准响应
            wrapped_response = success_response(
                data=data,
                message="请求成功",
                code=response.status_code
            )

            print(f"创建包装响应: {wrapped_response.model_dump()}")

            return JSONResponse(
                status_code=response.status_code,
                content=wrapped_response.model_dump(),
                headers=dict(response.headers)
            )

        except Exception as e:
            # 如果包装失败，返回原响应
            print(f"响应包装失败: {e}")
            import traceback
            traceback.print_exc()
            return response
        