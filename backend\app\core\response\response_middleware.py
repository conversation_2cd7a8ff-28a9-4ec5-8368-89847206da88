"""
backend/app/core/response/response_middleware.py
响应中间件：自动包装所有响应为标准结构
"""
import json
from typing import Callable

from fastapi import Request, Response
from fastapi.responses import StreamingResponse

from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .error_response import success_response


class ResponseMiddleware(BaseHTTPMiddleware):
    """响应包装中间件"""

    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: list[str] = None,
        exclude_methods: list[str] = None
    ):
        """
        初始化响应中间件

        Args:
            app: ASGI应用
            exclude_paths: 排除的路径列表（不进行包装）
            exclude_methods: 排除的HTTP方法列表
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json", "/favicon.ico",
            "/static", "/ws", "/websocket"  # 排除文档、静态文件、WebSocket
        ]
        # 确保包含OpenAPI相关路径---阿里ai修复内容：
        # if "/openapi.json" not in self.exclude_paths:
        #     self.exclude_paths.append("/openapi.json")
        self.exclude_methods = exclude_methods or ["OPTIONS", "HEAD"]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""

        # 检查是否需要跳过包装
        if self._should_skip_wrapping(request):
            return await call_next(request)

        # 执行请求
        response = await call_next(request)

        # 只处理JSON响应
        if not self._is_json_response(response):
            return response

        # 检查响应是否已经是标准格式
        if await self._is_already_wrapped(response):
            return response

        # 包装响应
        return await self._wrap_response(response)

    def _should_skip_wrapping(self, request: Request) -> bool:
        """判断是否应该跳过包装"""
        # 检查路径
        for path in self.exclude_paths:
            if request.url.path.startswith(path):
                return True

        # 检查方法
        if request.method in self.exclude_methods:
            return True

        return False

    def _is_json_response(self, response: Response) -> bool:
        """判断是否为JSON响应"""
        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type
    
    def safe_response(self,content: bytes, original: Response) -> Response:
        return StreamingResponse(
            content=iter([content]),
            status_code=original.status_code,
            media_type=original.media_type,
            headers={k: v for k, v in original.headers.items() if k.lower() != "content-length"}
        )


    async def _is_already_wrapped(self, response: Response) -> bool:
        """检查响应是否已经被包装"""
        try:
            # 读取响应体
            body = b""
            async for chunk in response.body_iterator:
                body += chunk

            # 尝试解析JSON
            data = json.loads(body.decode())

            # 检查是否包含标准字段
            if isinstance(data, dict) and "success" in data:
                # 重新创建响应（因为body_iterator已被消费）
                response._content = body
                return True

        except (json.JSONDecodeError, UnicodeDecodeError):
            pass

        return False

    async def _wrap_response(self, response: Response) -> JSONResponse:
        """包装响应为标准格式"""
        try:
            # 读取原始响应体
            body = await response.body()

            # 解析响应数据
            if body:
                try:
                    data = json.loads(body.decode())
                except json.JSONDecodeError:
                    data = body.decode()
            else:
                data = None

            # 创建标准响应
            wrapped_response = success_response(
                data=data,
                message="请求成功",
                code=response.status_code
            )

            return JSONResponse(
                status_code=response.status_code,
                content=wrapped_response.model_dump()
            )

        except Exception:
            # 如果包装失败，返回原响应
            return self.safe_response(body, response)
        