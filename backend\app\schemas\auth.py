"""
backend/app/schemas/auth.py
认证登录/响应模型
"""
from pydantic import BaseModel,Field

# 认证用户模型
class AuthUser(BaseModel):
    uid:int = Field(..., description="主键")
    username:str=Field(..., description="用户名")   
    is_superuser:bool=Field(..., description="是否为管理员")
    is_active:bool=Field(..., description="是否激活")

# 菜单元数据
class MenuMeta(BaseModel):
    affix: bool | None = Field(None, description="是否固定标签页")
    cache: bool | None = Field(None, description="是否缓存页面")
    new_window: bool | None = Field(None, description="是否新窗口打开")
    hidden: bool | None = Field(None, description="是否隐藏")
    icon: str | None = Field(None, description="图标")
    title: str | None = Field(None, description="菜单标题（可用于国际化）")

#单个一级菜单结构
# 用于登录之后的数据，返回嵌套层和前端渲染使用内容
class LoginResponseMenu(BaseModel):
    uid: int = Field(..., description="主键")
    name: str = Field(..., description="菜单名称")
    path: str = Field(..., description="路径")
    sort: int|None = Field(None, description="排序")
    component: str|None  = Field(None, description="组件")
    redirect: str|None  = Field(None, description="重定向")
    meta: MenuMeta | None = Field(None, description="元数据")
    requires_auth: bool = Field(..., description="是否需要权限")
    children: list["LoginResponseMenu"] | None = Field(None, description="子菜单")

LoginResponseMenu.model_rebuild()

# 按钮权限
class ButtonPermission(BaseModel):
    uid: int = Field(..., description="主键")
    name: str = Field(..., description="按钮名称")
    operation: str = Field(..., description="操作类型")

# 包含菜单信息的令牌模式
class TokenWithMenus(BaseModel):
    token: str = Field(..., description="访问令牌")
    menus: list[LoginResponseMenu] = Field(..., description="可访问菜单列表")
    buttons: list[ButtonPermission] = Field(..., description="可操作按钮权限列表")
    user: AuthUser = Field(..., description="用户信息")

