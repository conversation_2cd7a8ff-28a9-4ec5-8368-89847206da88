"""
backend/app/core/database.py
数据库连接管理
"""
from pathlib import Path # 路径管理
from urllib.parse import urlparse # URL解析
from sqlalchemy import create_engine # 数据库连接
from sqlalchemy.orm import sessionmaker # 会话管理
from sqlalchemy.ext.declarative import declarative_base # 模型基类
from contextlib import contextmanager # 上下文管理器

from app.core.config import settings 

# 获取数据库连接URL
def get_database_url():
    if settings.DB_TPYE == "sqlite":
        return settings.DB_SQLITE_PATH
    elif settings.DB_TPYE == "mysql":
        return f"mysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
    else:
        raise ValueError(f"不支持的数据库类型: {settings.DB_TPYE}")
    
# 获取数据库连接参数
def get_connect_args():
    if settings.DB_TPYE == "sqlite":
        return settings.DB_SQLITE_CONNECT_ARGS
    else:
        return {}

engine=create_engine(
    get_database_url(),
    connect_args=get_connect_args(),
    pool_pre_ping=True,  # 检查连接是否有效 
    echo=settings.DEBUG, # 打印 SQL 日志
)

#创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
# 创建基础模型类
Base = declarative_base()

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
#自动提交和回滚操作 transactional_session() 用于脚本或服务层，不用于 FastAPI 路由依赖注入。
@contextmanager
def transactional_session():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# 创建数据库表
def create_tables():
    Base.metadata.create_all(bind=engine)
    
# 删除sqlite数据库
def delete_sqlite_db():
    parsed = urlparse(settings.DB_SQLITE_PATH)
    relative_path = Path(parsed.path.lstrip("/"))# 去掉前导斜杠，避免拼接错误
    db_path =  Path(__file__).parent.parent.parent / relative_path # 拼接项目根目录
    if db_path.exists():
        db_path.unlink()
        print(f"✅ 删除 sqlite 数据库: {db_path}")
    else:
        print(f"❌ sqlite 数据库不存在: {db_path}")

# 删除mysql数据库
def delete_mysql_db():
    import mysql.connector
    conn = mysql.connector.connect(
        host=settings.DB_HOST,
        user=settings.DB_USER,
        password=settings.DB_PASSWORD,
    )
    cursor = conn.cursor()
    db_name = settings.DB_NAME

    # 检查数据库是否存在
    cursor.execute("SHOW DATABASES LIKE %s", (db_name,))
    result = cursor.fetchone()
    if result:
        cursor.execute(f"DROP DATABASE `{db_name}`")
        print(f"✅ 已删除 MySQL 数据库: {db_name}")
    else:
        print(f"⚠️ 数据库不存在: {db_name}")

    cursor.close()
    conn.close()

# 删除数据库 统一接口
def delete_database():
    if settings.DB_TPYE == "sqlite":
        delete_sqlite_db()
    elif settings.DB_TPYE == "mysql":
        delete_mysql_db()
    else:
        print(f"❌ 不支持的数据库类型: {settings.DB_TPYE}")
