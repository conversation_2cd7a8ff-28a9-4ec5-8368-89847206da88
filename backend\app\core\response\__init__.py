"""
backend/app/core/response/__init__.py
统一导出接口，方便引用
"""

# 响应模型
from .error_response import (
    ApiResponse,
    success_response,
    error_response
)

# 自定义异常
from .exceptions import (
    BizException,
    ValidationException,
    AuthenticationException,
    AuthorizationException,
    NotFoundException,
    ConflictException,
    RateLimitException
)

# 异常处理器
from .exception_handlers import (
    biz_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    generic_exception_handler,
    register_exception_handlers
)

# 响应包装器
from .response_wrapper import (
    wrap_response,
    success,
    created,
    accepted
)

# 响应中间件
from .response_middleware import ResponseMiddleware

__all__ = [
    # 响应模型
    "ApiResponse",
    "success_response",
    "error_response",

    # 自定义异常
    "BizException",
    "ValidationException",
    "AuthenticationException",
    "AuthorizationException",
    "NotFoundException",
    "ConflictException",
    "RateLimitException",

    # 异常处理器
    "biz_exception_handler",
    "http_exception_handler",
    "validation_exception_handler",
    "generic_exception_handler",
    "register_exception_handlers",

    # 响应包装器
    "wrap_response",
    "success",
    "created",
    "accepted",

    # 响应中间件
    "ResponseMiddleware"
]
