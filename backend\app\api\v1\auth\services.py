"""
backend/app/api/v1/auth/services.py
权限业务逻辑
"""
from datetime import timedelta
from fastapi import HTTPException
from sqlalchemy.sql import func

from app.core.config import settings
from app.api.v1.user.schemas import UserLoginRequest
from app.models.user import User
from app.core.database import transactional_session
from app.core.security import create_access_token,verify_password
from app.schemas.auth import TokenWithMenus
from app.api.v1.menu.services import get_user_access_data
from app.api.v1.user.services import get_AuthUser


def login_user(data:UserLoginRequest)->TokenWithMenus:
    print(data.username,data.password)
    with transactional_session() as db:
        user=db.query(User).filter(User.username==data.username).first()
        if not user:
            raise HTTPException(status_code=401, detail="用户不存在")
        if not verify_password(data.password,user.password):
            raise HTTPException(status_code=401, detail="密码错误")
        
        if not user.is_active:
            raise HTTPException(status_code=401, detail="用户未激活")
        
        #更新用户登录时间
        user.last_login_at = func.now()

        # 创建令牌
        access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(user.uid, expires_delta=access_token_expires)

        #获取用户信息
        authuser=get_AuthUser(user)
        #获取用户可用菜单和按钮
        menus,buttons=get_user_access_data(user)
        #构建返回数据
        token_with_menus=TokenWithMenus(
            token=access_token,
            menus=menus,
            buttons=buttons,
            user=authuser
        )

        return token_with_menus