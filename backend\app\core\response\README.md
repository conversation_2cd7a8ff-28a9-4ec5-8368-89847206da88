# 响应管理系统使用指南

## 概述

这是一个完整的响应管理系统，实现了"零感知、自动处理、统一响应结构"的目标。开发者可以专注于业务逻辑，响应结构会自动标准化。

## 核心组件

### 1. 响应模型 (`error_response.py`)
定义统一的API响应结构：
```python
{
    "success": bool,      # 请求是否成功
    "data": any,         # 响应数据
    "message": str,      # 响应消息
    "code": int,         # 响应代码
    "error": str         # 错误信息（可选）
}
```

### 2. 自定义异常 (`exceptions.py`)
提供业务异常类，支持自动错误处理：
- `BizException` - 业务异常基类
- `ValidationException` - 参数验证异常
- `AuthenticationException` - 认证异常
- `AuthorizationException` - 授权异常
- `NotFoundException` - 资源不存在异常
- `ConflictException` - 资源冲突异常
- `RateLimitException` - 频率限制异常

### 3. 响应中间件 (`response_middleware.py`)
自动包装所有响应为标准结构，支持：
- 自动检测和包装JSON响应
- 排除特定路径（文档、静态文件、WebSocket等）
- 智能判断是否已被包装

### 4. 异常处理器 (`exception_handlers.py`)
捕获并格式化各种异常：
- 业务异常处理
- HTTP异常处理
- 参数验证异常处理
- 通用异常处理

### 5. 响应包装器 (`response_wrapper.py`)
可选的手动装饰器，用于特定接口：
- `@wrap_response()` - 基础包装器
- `@success()` - 成功响应
- `@created()` - 创建成功响应
- `@accepted()` - 请求已接受响应

## 使用方式

### 1. 全局启用（推荐）

在 `main.py` 中注册中间件和异常处理器：

```python
from fastapi import FastAPI
from app.core.response import ResponseMiddleware, register_exception_handlers

app = FastAPI()

# 注册响应中间件（全局自动包装）
app.add_middleware(ResponseMiddleware)

# 注册异常处理器
register_exception_handlers(app)
```

### 2. 业务代码示例

```python
from app.core.response import BizException, NotFoundException

# 普通接口 - 自动包装
@app.get("/users/{user_id}")
async def get_user(user_id: int):
    user = get_user_from_db(user_id)
    if not user:
        # 抛出业务异常，自动转换为错误响应
        raise NotFoundException("用户不存在")
    
    # 直接返回数据，自动包装为成功响应
    return user

# 使用装饰器的接口
from app.core.response import created

@app.post("/users")
@created("用户创建成功")
async def create_user(user_data: UserCreate):
    user = create_user_in_db(user_data)
    return user
```

### 3. 响应示例

成功响应：
```json
{
    "success": true,
    "data": {"id": 1, "name": "张三"},
    "message": "请求成功",
    "code": 200,
    "error": null
}
```

错误响应：
```json
{
    "success": false,
    "data": null,
    "message": "用户不存在",
    "code": 404,
    "error": "NotFoundException"
}
```

## 配置选项

### 中间件配置
```python
app.add_middleware(
    ResponseMiddleware,
    exclude_paths=["/docs", "/custom-api"],  # 排除路径
    exclude_methods=["OPTIONS", "HEAD"]      # 排除方法
)
```

### 异常配置
```python
# 静默异常（不包装响应）
raise BizException("内部错误", silent=True)

# 带详细错误信息
raise BizException(
    message="操作失败",
    code=400,
    error_detail="详细错误信息"
)
```

## 最佳实践

1. **全局启用中间件**：让所有接口自动获得标准响应格式
2. **使用业务异常**：在服务层抛出具体的业务异常
3. **合理使用装饰器**：对于需要特定消息的接口使用装饰器
4. **配置排除路径**：排除不需要包装的特殊接口
5. **开发环境调试**：利用DEBUG模式获取详细错误信息

## 灵活性设计

- **可选包装器**：装饰器是可选的，只在需要时使用
- **智能中间件**：自动判断是否需要包装，支持排除规则
- **静默异常**：支持跳过包装的内部异常
- **兼容性**：与现有代码完全兼容，渐进式升级
