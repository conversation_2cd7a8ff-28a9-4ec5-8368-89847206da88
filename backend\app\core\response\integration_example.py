"""
backend/app/core/response/integration_example.py
响应系统集成示例 - 展示如何在实际项目中使用
"""
from fastapi import FastAPI, Depends
from pydantic import BaseModel

# 导入响应系统
from . import (
    ResponseMiddleware,
    register_exception_handlers,
    BizException,
    NotFoundException,
    ValidationException,
    success,
    created,
    accepted
)

# 示例数据模型
class User(BaseModel):
    id: int
    name: str
    email: str

class UserCreate(BaseModel):
    name: str
    email: str

class UserUpdate(BaseModel):
    name: str = None
    email: str = None

# 模拟数据库
fake_users_db = {
    1: User(id=1, name="张三", email="<EMAIL>"),
    2: User(id=2, name="李四", email="<EMAIL>"),
}

# 创建应用并配置响应系统
def create_app() -> FastAPI:
    """创建配置好响应系统的FastAPI应用"""
    app = FastAPI(title="响应系统集成示例")
    
    # 1. 注册响应中间件（全局自动包装）
    app.add_middleware(
        ResponseMiddleware,
        exclude_paths=["/docs", "/redoc", "/openapi.json", "/health"],
        exclude_methods=["OPTIONS", "HEAD"]
    )
    
    # 2. 注册异常处理器
    register_exception_handlers(app)
    
    return app

app = create_app()

# 业务服务层示例
class UserService:
    """用户服务 - 展示如何在服务层使用异常"""
    
    @staticmethod
    def get_user(user_id: int) -> User:
        """获取用户"""
        if user_id not in fake_users_db:
            raise NotFoundException(f"用户 {user_id} 不存在")
        return fake_users_db[user_id]
    
    @staticmethod
    def create_user(user_data: UserCreate) -> User:
        """创建用户"""
        # 检查邮箱是否已存在
        for user in fake_users_db.values():
            if user.email == user_data.email:
                raise ValidationException(f"邮箱 {user_data.email} 已被使用")
        
        # 创建新用户
        new_id = max(fake_users_db.keys()) + 1
        new_user = User(id=new_id, **user_data.dict())
        fake_users_db[new_id] = new_user
        return new_user
    
    @staticmethod
    def update_user(user_id: int, user_data: UserUpdate) -> User:
        """更新用户"""
        if user_id not in fake_users_db:
            raise NotFoundException(f"用户 {user_id} 不存在")
        
        user = fake_users_db[user_id]
        update_data = user_data.dict(exclude_unset=True)
        
        # 检查邮箱冲突
        if "email" in update_data:
            for uid, u in fake_users_db.items():
                if uid != user_id and u.email == update_data["email"]:
                    raise ValidationException(f"邮箱 {update_data['email']} 已被使用")
        
        # 更新用户
        for field, value in update_data.items():
            setattr(user, field, value)
        
        return user
    
    @staticmethod
    def delete_user(user_id: int) -> None:
        """删除用户"""
        if user_id not in fake_users_db:
            raise NotFoundException(f"用户 {user_id} 不存在")
        del fake_users_db[user_id]

# API路由示例
@app.get("/health")
async def health_check():
    """健康检查 - 不会被包装（在排除路径中）"""
    return {"status": "ok"}

@app.get("/users")
async def list_users():
    """获取用户列表 - 自动包装"""
    return list(fake_users_db.values())

@app.get("/users/{user_id}")
async def get_user(user_id: int):
    """获取单个用户 - 自动包装，自动异常处理"""
    return UserService.get_user(user_id)

@app.post("/users")
@created("用户创建成功")
async def create_user(user_data: UserCreate):
    """创建用户 - 手动包装消息"""
    return UserService.create_user(user_data)

@app.put("/users/{user_id}")
@success("用户更新成功")
async def update_user(user_id: int, user_data: UserUpdate):
    """更新用户 - 手动包装消息"""
    return UserService.update_user(user_id, user_data)

@app.delete("/users/{user_id}")
@accepted("用户删除请求已接受")
async def delete_user(user_id: int):
    """删除用户 - 异步处理响应"""
    UserService.delete_user(user_id)
    return {"message": "用户已删除"}

@app.get("/users/{user_id}/profile")
async def get_user_profile(user_id: int):
    """获取用户详细信息 - 复杂业务逻辑示例"""
    user = UserService.get_user(user_id)
    
    # 模拟复杂业务逻辑
    if user.id == 1:
        # 特殊用户，添加额外信息
        profile = {
            "user": user.dict(),
            "role": "admin",
            "permissions": ["read", "write", "delete"],
            "last_login": "2024-01-01T00:00:00Z"
        }
    else:
        # 普通用户
        profile = {
            "user": user.dict(),
            "role": "user", 
            "permissions": ["read"],
            "last_login": None
        }
    
    return profile

@app.post("/users/{user_id}/activate")
async def activate_user(user_id: int):
    """激活用户 - 业务异常示例"""
    user = UserService.get_user(user_id)
    
    # 模拟业务规则检查
    if user.id == 1:
        raise BizException("管理员账户无需激活", code=400)
    
    # 模拟激活逻辑
    return {"message": f"用户 {user.name} 已激活"}

# 错误处理示例
@app.get("/test/server-error")
async def test_server_error():
    """测试服务器错误 - 会被通用异常处理器捕获"""
    raise Exception("这是一个模拟的服务器错误")

@app.get("/test/custom-error")
async def test_custom_error():
    """测试自定义错误"""
    raise BizException(
        message="自定义业务错误",
        code=400,
        error_detail="这是详细的错误信息"
    )

# 使用示例说明
"""
使用这个响应系统的优势：

1. 零感知开发：
   - 开发者只需要关注业务逻辑
   - 返回数据会自动包装为标准格式
   - 异常会自动转换为错误响应

2. 统一响应格式：
   - 所有API都返回相同的响应结构
   - 前端可以统一处理响应
   - 便于API文档生成

3. 灵活配置：
   - 可以排除特定路径（如健康检查）
   - 可以使用装饰器自定义消息
   - 支持不同类型的业务异常

4. 开发友好：
   - 在开发环境提供详细错误信息
   - 支持静默异常处理
   - 完全向后兼容

示例响应格式：

成功响应：
{
    "success": true,
    "data": {"id": 1, "name": "张三", "email": "<EMAIL>"},
    "message": "请求成功",
    "code": 200,
    "error": null
}

错误响应：
{
    "success": false,
    "data": null,
    "message": "用户 999 不存在",
    "code": 404,
    "error": "NotFoundException"
}
"""
