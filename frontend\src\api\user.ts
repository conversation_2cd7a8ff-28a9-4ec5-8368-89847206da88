/**
 * 用户相关的接口
 * frontend/src/api/user.ts
 */

import qs from 'qs'
import {post} from '@/utils/request'
import type { loginData,TokenWithMenus } from '@/types/user'

// 用户登录接口，使用封装post方法发送
//后端使用 OAuth2PasswordRequestForm  作为登录标准， 所以要补充相关格式信息
export function login(data: loginData) {
  return post<TokenWithMenus>('/auth/login', qs.stringify({
    grant_type:'password',
    username:data.username,
    password:data.password,
  }),{
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}