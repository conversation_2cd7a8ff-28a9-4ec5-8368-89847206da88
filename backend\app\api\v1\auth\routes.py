"""
backend/app/api/v1/auth/routes.py
权限路由
"""

from fastapi import APIRouter,Depends
from fastapi.security import OAuth2PasswordRequestForm # OAuth2密码模式

from app.schemas.auth import TokenWithMenus
from app.api.v1.auth.services import login_user
from app.api.v1.user.schemas import UserLoginRequest
from app.core.response.response_wrapper import wrap_response

router = APIRouter()

@router.post("/login",response_model=TokenWithMenus,summary="用户登录")
@wrap_response
async def login(from_data:OAuth2PasswordRequestForm=Depends()):
    # 实现登录逻辑,返回对应的菜单
    login_data = UserLoginRequest(username=from_data.username, password=from_data.password)
    return login_user(login_data)

