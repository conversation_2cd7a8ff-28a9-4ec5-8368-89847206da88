<template>
    <n-flex justify="center" align="center" style="height: 100vh; ">
        <n-card title="用户登录" hoverable style="width: 500px;">
            <n-form ref="formRef" :model="data" :rules="rules">
                <n-form-item path="username" label="用户名">
                <n-input ref="usernameInput"  v-model:value="data.username" placeholder="请输入用户名" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="密码">
                <n-input
                    v-model:value="data.password"
                    type="password"
                    placeholder="请输入密码"
                    @keydown.enter.prevent
                />
                </n-form-item>
                <n-grid :cols="1">
                  <n-gi>
                    <n-flex justify="end">
                      <n-button
                          :disabled="!data.username || !data.password"
                          round
                          type="primary"

                          @click="handleLogin"
                      >
                          登录
                      </n-button>
                    </n-flex>
                  </n-gi>          
                </n-grid>
            </n-form>
        </n-card>
    </n-flex>
</template>

<script lang="ts" setup>

import {  onMounted, ref } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'

import type { loginData } from '@/types/user'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { useNotify } from '@/utils/notify'
import { extractErrorMessage } from '@/utils/error'
const { showSuccess, showError } = useNotify() //实例化封装的消息通知
const formRef = ref<FormInst | null>(null)

const data = ref<loginData>({
  username: 'admin',
  password: '123456'
})

const rules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: ['blur', 'input'] }],
  password: [{ required: true, message: '请输入密码', trigger: ['blur', 'input'] }]
}

const router = useRouter()
const userStore = useUserStore()

function handleLogin() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      try{
        await userStore.login(data.value)     
        showSuccess('登录成功')
        // 登录成功后，动态路由将在路由守卫中自动注册
        userStore.setDynamicRoutesInitialized(false) // 重置状态，确保守卫中重新注册
        router.replace({ path: '/' } )
        console.log(router.getRoutes().map(r => ({ name: r.name, path: r.path })))
      }catch(err){
        showError('登录失败: ' + extractErrorMessage(err))
      }
      // TODO: 发起 API 请求
    } else {
      showError('请填写完整信息')
    }
  })
}

//模拟用户点击。防止getRangeAt(0) 报错，原因是：Naive UI 的 message.success()依赖浏览器建立一个 Selection 对象
const usernameInput = ref<HTMLInputElement | null>(null)
onMounted(()=>{
 usernameInput.value?.focus()
})
</script>

<style>

</style>