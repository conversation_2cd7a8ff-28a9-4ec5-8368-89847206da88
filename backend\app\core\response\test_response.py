"""
backend/app/core/response/test_response.py
响应系统测试示例
"""
import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from . import (
    ResponseMiddleware,
    register_exception_handlers,
    BizException,
    NotFoundException,
    success,
    created,
    success_response,
    error_response
)

# 创建测试应用
app = FastAPI()

# 注册中间件和异常处理器
app.add_middleware(ResponseMiddleware)
register_exception_handlers(app)


# 测试路由
@app.get("/test/auto-wrap")
async def test_auto_wrap():
    """测试自动包装"""
    return {"message": "这是原始数据"}


@app.get("/test/manual-wrap")
@success("手动包装成功")
async def test_manual_wrap():
    """测试手动包装"""
    return {"data": "手动包装的数据"}


@app.get("/test/created")
@created("资源创建成功")
async def test_created():
    """测试创建响应"""
    return {"id": 1, "name": "新资源"}


@app.get("/test/biz-exception")
async def test_biz_exception():
    """测试业务异常"""
    raise BizException("这是一个业务异常", code=400)


@app.get("/test/not-found")
async def test_not_found():
    """测试404异常"""
    raise NotFoundException("资源未找到")


@app.get("/test/validation-error")
async def test_validation_error(required_param: str):
    """测试参数验证异常"""
    return {"param": required_param}


# 测试客户端
client = TestClient(app)


def test_auto_wrap_response():
    """测试自动包装响应"""
    response = client.get("/test/auto-wrap")
    assert response.status_code == 200
    
    data = response.json()
    assert data["success"] is True
    assert data["message"] == "请求成功"
    assert data["code"] == 200
    assert data["data"]["message"] == "这是原始数据"


def test_manual_wrap_response():
    """测试手动包装响应"""
    response = client.get("/test/manual-wrap")
    assert response.status_code == 200
    
    data = response.json()
    assert data["success"] is True
    assert data["message"] == "手动包装成功"
    assert data["code"] == 200
    assert data["data"]["data"] == "手动包装的数据"


def test_created_response():
    """测试创建响应"""
    response = client.get("/test/created")
    assert response.status_code == 201
    
    data = response.json()
    assert data["success"] is True
    assert data["message"] == "资源创建成功"
    assert data["code"] == 201
    assert data["data"]["id"] == 1


def test_biz_exception_response():
    """测试业务异常响应"""
    response = client.get("/test/biz-exception")
    assert response.status_code == 400
    
    data = response.json()
    assert data["success"] is False
    assert data["message"] == "这是一个业务异常"
    assert data["code"] == 400
    assert data["data"] is None


def test_not_found_response():
    """测试404异常响应"""
    response = client.get("/test/not-found")
    assert response.status_code == 404
    
    data = response.json()
    assert data["success"] is False
    assert data["message"] == "资源未找到"
    assert data["code"] == 404


def test_validation_error_response():
    """测试参数验证异常响应"""
    response = client.get("/test/validation-error")  # 缺少必需参数
    assert response.status_code == 422
    
    data = response.json()
    assert data["success"] is False
    assert data["message"] == "参数验证失败"
    assert data["code"] == 422


def test_response_models():
    """测试响应模型"""
    # 测试成功响应
    success_resp = success_response(data={"test": "data"}, message="测试成功")
    assert success_resp.success is True
    assert success_resp.data == {"test": "data"}
    assert success_resp.message == "测试成功"
    assert success_resp.code == 200
    
    # 测试错误响应
    error_resp = error_response(message="测试错误", code=400, error="TestError")
    assert error_resp.success is False
    assert error_resp.data is None
    assert error_resp.message == "测试错误"
    assert error_resp.code == 400
    assert error_resp.error == "TestError"


if __name__ == "__main__":
    # 运行简单测试
    print("🧪 开始测试响应系统...")
    
    # 测试响应模型
    test_response_models()
    print("✅ 响应模型测试通过")
    
    # 测试API响应
    test_auto_wrap_response()
    print("✅ 自动包装测试通过")
    
    test_manual_wrap_response()
    print("✅ 手动包装测试通过")
    
    test_biz_exception_response()
    print("✅ 业务异常测试通过")
    
    test_not_found_response()
    print("✅ 404异常测试通过")
    
    test_validation_error_response()
    print("✅ 参数验证异常测试通过")
    
    print("🎉 所有测试通过！响应系统工作正常。")
