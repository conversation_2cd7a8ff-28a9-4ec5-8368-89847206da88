"""
backend/app/core/response/test_fix.py
测试响应中间件修复
"""
import asyncio
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from fastapi.responses import StreamingResponse, JSONResponse

from . import (
    ResponseMiddleware,
    register_exception_handlers,
    NotFoundException,
    success
)

# 创建测试应用
app = FastAPI()

# 注册中间件和异常处理器
app.add_middleware(ResponseMiddleware)
register_exception_handlers(app)


@app.get("/test/normal")
async def test_normal():
    """测试普通响应"""
    return {"message": "这是普通响应"}


@app.get("/test/streaming")
async def test_streaming():
    """测试流式响应"""
    def generate():
        for i in range(3):
            yield f"data chunk {i}\n"
    
    return StreamingResponse(generate(), media_type="text/plain")


@app.get("/test/json-response")
async def test_json_response():
    """测试JSONResponse"""
    return JSONResponse(content={"message": "这是JSONResponse"})


@app.get("/test/already-wrapped")
async def test_already_wrapped():
    """测试已包装的响应"""
    return JSONResponse(content={
        "success": True,
        "data": {"message": "已包装"},
        "message": "请求成功",
        "code": 200,
        "error": None
    })


@app.get("/test/exception")
async def test_exception():
    """测试异常处理"""
    raise NotFoundException("测试资源不存在")


@app.get("/test/decorator")
@success("装饰器测试成功")
async def test_decorator():
    """测试装饰器"""
    return {"data": "装饰器数据"}


def test_responses():
    """测试所有响应类型"""
    client = TestClient(app)
    
    print("🧪 开始测试响应中间件修复...")
    
    # 测试普通响应
    print("\n1. 测试普通响应...")
    response = client.get("/test/normal")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "message" in data["data"]
    print("✅ 普通响应测试通过")
    
    # 测试流式响应
    print("\n2. 测试流式响应...")
    response = client.get("/test/streaming")
    print(f"状态码: {response.status_code}")
    print(f"内容类型: {response.headers.get('content-type')}")
    print(f"响应内容: {response.text}")
    assert response.status_code == 200
    assert "text/plain" in response.headers.get("content-type", "")
    print("✅ 流式响应测试通过")
    
    # 测试JSONResponse
    print("\n3. 测试JSONResponse...")
    response = client.get("/test/json-response")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    print("✅ JSONResponse测试通过")
    
    # 测试已包装响应
    print("\n4. 测试已包装响应...")
    response = client.get("/test/already-wrapped")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["message"] == "已包装"
    print("✅ 已包装响应测试通过")
    
    # 测试异常处理
    print("\n5. 测试异常处理...")
    response = client.get("/test/exception")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 404
    data = response.json()
    assert data["success"] is False
    assert "不存在" in data["message"]
    print("✅ 异常处理测试通过")
    
    # 测试装饰器
    print("\n6. 测试装饰器...")
    response = client.get("/test/decorator")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"] == "装饰器测试成功"
    print("✅ 装饰器测试通过")
    
    print("\n🎉 所有测试通过！响应中间件修复成功。")


if __name__ == "__main__":
    test_responses()
