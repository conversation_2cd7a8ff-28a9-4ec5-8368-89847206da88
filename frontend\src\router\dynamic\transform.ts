// src/router/dynamic/transform.ts
import type { RouteRecordRaw } from 'vue-router'
import type { LoginResponseMenu } from '@/types/menu'

const views = import.meta.glob('@/views/**/*.vue')

function resolveComponentPath(component: string): string {
  return component.replace(/\/$/, '')
}

function createRoute(menu: LoginResponseMenu): RouteRecordRaw | null {
  if (!menu.component || !menu.component.trim()) return null

  const componentPath = resolveComponentPath(menu.component)
  const key = `/src/views/${componentPath}.vue`

  if (!views[key]) {
    console.warn(`组件未找到: ${key}`)
    return null
  }

  return {
    path: menu.path,
    name: menu.name,
    component: views[key],
    meta: {
      title: menu.meta?.title ?? menu.name,
      icon: menu.meta?.icon,
      affix: menu.meta?.affix ?? false,
      requiresAuth: menu.requires_auth ?? true
    },
    redirect: menu.redirect?.trim() || undefined,
    children: []
  }
}

export function transformMenuToRoutes(menu: LoginResponseMenu): RouteRecordRaw[] {
  const children = menu.children?.flatMap(transformMenuToRoutes) ?? []
  const route = createRoute(menu)
  return route ? [route, ...children] : children
}

export function flattenMenusToRoutes(menus: LoginResponseMenu[]): RouteRecordRaw[] {
  return menus.flatMap(transformMenuToRoutes)
}