/**
 * frontend/src/main.ts
 * 应用程序入口
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { setupNaiveUI } from './plugins/naive'
import { useUserStore } from '@/stores/user'
import { registerDynamicRoutes } from './router/dynamic/index'

import App from './App.vue'
import router from './router'

const app = createApp(App) //创建应用实例

const pinia = createPinia() //创建 pinia 实例
pinia.use(piniaPluginPersistedstate) //持久化
app.use(pinia)

app.use(router)
setupNaiveUI(app)

// 恢复用户状态
const userStore = useUserStore()
userStore.restore()

// 初始化应用
async function initializeApp() {
  // 如果用户已登录，预先注册动态路由
  if (userStore.isLoggedIn && userStore.menus.length > 0) {
    console.log('用户已登录，预先注册动态路由')
    try {
      const success = await registerDynamicRoutes(router, userStore.menus)
      if (success) {
        userStore.setDynamicRoutesInitialized(true)
        console.log('预先注册动态路由成功')
      }
    } catch (error) {
      console.error('预先注册动态路由失败:', error)
    }
  } else {
    console.log('用户未登录或无菜单数据')
  }
  // 挂载应用
  app.mount('#app')
}

initializeApp()

