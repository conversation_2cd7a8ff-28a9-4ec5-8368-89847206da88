"""
backend/app/seeds/roles.py
角色数据填充
"""

from app.models.role import Role
from app.core.database import transactional_session #自动提交

def seed_roles():
   with transactional_session() as db:
        existing = db.query(Role).count()
        if existing==0:
            admin_role = Role(name="admin", description="系统管理员")
            user_role = Role(name="user", description="普通用户",is_default=True)
            it_manager_role = Role(name="it_manager", description="负责IT部门事务管理")
            frontend_role = Role(name="frontend_engineer", description="负责前端运维")
            backend_role = Role(name="backend_engineer", description="负责后端运维")
            
            db.add_all([admin_role, user_role, it_manager_role, frontend_role, backend_role])
