"""
backend/app/models/user.py
用户模型
"""
from typing import TYPE_CHECKING
from sqlalchemy import String,Integer,Boolean,DateTime,ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base
from app.models.base import TimeStampedModel
from app.models.association import User_Role

if TYPE_CHECKING:
    from app.models.department import Department

class User(Base,TimeStampedModel):
    """
    用户模型
    """
    __tablename__="users"
    username:Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    password:Mapped[str] = mapped_column(String(255), nullable=False, comment="加密密码")
    department_id:Mapped[int] = mapped_column(Integer,ForeignKey("departments.uid"),nullable=True,comment="部门ID")
    is_active:Mapped[bool]=mapped_column(Boolean,default=True,comment="是否激活")
    is_superuser:Mapped[bool]=mapped_column(<PERSON><PERSON><PERSON>,default=False,comment="管理员")

    realname:Mapped[str]=mapped_column(String(50),nullable=True,comment="真实姓名")
    email:Mapped[str] = mapped_column(String, unique=True, index=True, nullable=True, comment="邮箱")
    phone:Mapped[str] = mapped_column(String, unique=True, index=True, nullable=True, comment="手机号")
    avatar:Mapped[str] = mapped_column(String, nullable=True, comment="头像")
    
    last_login_at:Mapped[DateTime | None] = mapped_column(DateTime(timezone=True), nullable=True, comment="最后登录时间")

    user_role:Mapped[list["User_Role"]]=relationship(back_populates="user", lazy="selectin") #增加lazy=selectin 一次性查询 避免N+1
    department:Mapped["Department"]=relationship(back_populates="users")