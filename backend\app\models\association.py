"""
backend/app/models/association.py
通用关联模型，仅针对角色，用户，部门
"""
from typing import TYPE_CHECKING
from sqlalchemy import Integer,ForeignKey
from sqlalchemy.orm import relationship,Mapped, mapped_column

from app.core.database import Base

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.role import Role
    from app.models.menu import Menu

class User_Role(Base):
    # 用户角色关联表
    __tablename__="user_roles"
    user_id:Mapped[int] = mapped_column(Integer, ForeignKey("users.uid", ondelete="RESTRICT"), nullable=False,primary_key=True, comment="用户ID")
    role_id:Mapped[int] = mapped_column(Integer, ForeignKey("roles.uid", ondelete="RESTRICT"), nullable=False,primary_key=True, comment="角色ID")

    user:Mapped["User"]=relationship(back_populates="user_role",lazy="selectin")
    role:Mapped["Role"]=relationship(back_populates="user_role",lazy="selectin")

class Role_Menu(Base):
    # 角色菜单关联表
    __tablename__="role_menus"
    role_id:Mapped[int] = mapped_column(Integer, ForeignKey("roles.uid", ondelete="CASCADE"), nullable=False,primary_key=True, comment="角色ID")
    menu_id:Mapped[int] = mapped_column(Integer, ForeignKey("menus.uid", ondelete="CASCADE"), nullable=False,primary_key=True, comment="菜单ID")

    menu: Mapped["Menu"] = relationship(back_populates="role_links",lazy="selectin")
    role: Mapped["Role"] = relationship(back_populates="menu_links",lazy="selectin")