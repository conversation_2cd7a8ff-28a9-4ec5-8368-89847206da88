"""
backend/app/core/response/response_wrapper.py
响应包装器：手动装饰器，包装成功响应（可选使用）
"""
from functools import wraps
from typing import Callable, Any
from fastapi.responses import JSONResponse, Response

from .error_response import success_response


def wrap_response(message: str = "请求成功", code: int = 200):
    """
    响应包装装饰器

    Args:
        message: 成功消息
        code: 状态码

    Usage:
        @wrap_response("用户创建成功")
        async def create_user():
            return user_data
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> JSONResponse:
            result = await func(*args, **kwargs)
            return _wrap_result(result, message, code)

        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> JSONResponse:
            result = func(*args, **kwargs)
            return _wrap_result(result, message, code)

        # 判断是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def _wrap_result(result: Any, message: str, code: int) -> JSONResponse:
    """包装结果为标准响应"""
    # 如果已经是Response对象，直接返回
    if isinstance(result, Response):
        return result

    # 处理数据格式
    if hasattr(result, 'model_dump'):  # Pydantic 模型
        data = result.model_dump()
    elif hasattr(result, 'dict'):  # 其他字典类型对象
        data = result.dict()
    else:
        data = result

    # 创建标准响应
    response = success_response(data=data, message=message, code=code)
    return JSONResponse(
        status_code=code,
        content=response.model_dump()
    )


# 便捷装饰器
def success(message: str = "操作成功"):
    """成功响应装饰器"""
    return wrap_response(message=message, code=200)


def created(message: str = "创建成功"):
    """创建成功响应装饰器"""
    return wrap_response(message=message, code=201)


def accepted(message: str = "请求已接受"):
    """请求已接受响应装饰器"""
    return wrap_response(message=message, code=202)