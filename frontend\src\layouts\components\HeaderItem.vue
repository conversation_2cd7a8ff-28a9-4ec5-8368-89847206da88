<!-- frontend/src/layouts/components/HeaderItem.vue -->
 <!-- 顶部菜单项 -->
<template>
  <n-layout-header bordered style="height: 48px; padding: 0 24px;">
    <n-grid :cols="2" x-gap="12">
      <!-- 左侧 Logo + 系统名称 -->
      <n-gi>
        <n-flex>
          <img src="" alt="logo" style="height: 32px;" />
          <span style="margin-left: 12px; font-weight: bold; font-size: 18px;">系统模板</span>
        </n-flex>
      </n-gi>

      <!-- 右侧 用户头像 + 姓名 + 菜单 -->
      <n-gi>
        <n-flex justify="end" >
          <n-dropdown :options="userOptions" trigger="hover" placement="bottom-end" @select="handleSelect">
            <n-avatar
              round
              size="medium"
              :src="avatar"
              :fallback-src="fallbackAvatar"
              style="cursor: pointer;"
            />
          </n-dropdown>
          <span style="margin-left: 8px;">{{ username }}</span>
        </n-flex>
      </n-gi>
    </n-grid>
  </n-layout-header>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const router = useRouter()

const avatar = ref('https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg')
const fallbackAvatar = 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
const username = ref('阳光')

const userOptions = [
  {
    label: '个人中心',
    key: 'profile'
  },
  {
    label: '退出登录',
    key: 'logout'
  }
]

function handleSelect(key: string) {
  console.log("点击了",key)
  if (key === 'logout') {
    // TODO: 清除 token、跳转登录页
    userStore.logout()
    router.push({ name: 'login' })
  } else if (key === 'profile') {
    router.push({ path: '/me/profile' })
  }
}
</script>

<style>

</style>