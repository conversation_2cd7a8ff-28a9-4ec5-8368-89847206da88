"""
backend/app/core/security.py
安全相关功能：JWT令牌、密码加密等
"""
from datetime import  timedelta,datetime,timezone
from typing import Optional
from jose import JWTError, jwt,ExpiredSignatureError
from passlib.context import CryptContext



from app.core.config import settings

# 密码加密上下文
pwd_context = CryptContext(
    schemes=["pbkdf2_sha256"],
    deprecated="auto",
    pbkdf2_sha256__rounds=5000 # 增加迭代次数以提高安全性，根据硬件性能调整
)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    获取密码哈希值
    """
    return pwd_context.hash(password)


def create_access_token(uid:int, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建访问令牌
    """

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode=({"exp": expire,"sub":str(uid)})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """
    验证令牌并返回用户名
    """
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        uid:str= payload.get("sub")
        return int(uid) if uid else None
    except ExpiredSignatureError:
        # 令牌过期逻辑
        return None
    except JWTError:
        return None